{"name": "@ali/nr-resource-components", "version": "0.0.14", "description": "选投资源位表单配置控件库", "files": ["esm", "es2017", "cjs", "dist"], "main": "esm/index.js", "module": "esm/index.js", "types": "esm/index.d.ts", "exports": {".": {"es2017": {"types": "./es2017/index.d.ts", "default": "./es2017/index.js"}, "default": {"types": "./esm/index.d.ts", "default": "./esm/index.js"}}, "./*": "./*"}, "sideEffects": ["dist/*", "*.scss", "*.less", "*.css"], "scripts": {"start": "ice-pkg start", "build": "ice-pkg build", "prepublishOnly": "npm run build", "eslint": "eslint --cache --ext .js,.jsx,.ts,.tsx ./", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"**/*.{css,scss,less}\"", "lint": "npm run eslint && npm run stylelint"}, "keywords": ["ice", "react", "component"], "dependencies": {"@ice/jsx-runtime": "^0.2.0", "@swc/helpers": "^0.5.1", "ahooks": "^3.7.8", "axios": "^1.6.5", "bignumber.js": "^9.1.2", "lodash": "^4.17.21"}, "devDependencies": {"@alifd/next": "^1.27.2", "@alife/theme-nr-op": "^2.1.0", "@applint/spec": "^1.2.3", "@ice/pkg": "^1.0.0", "@ice/pkg-plugin-docusaurus": "^1.0.0", "@types/lodash": "^4.17.7", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "eslint": "^8.0.0", "moment": "^2.30.1", "react": "^18.0.0", "react-dom": "^18.0.0", "stylelint": "^15.0.0"}, "peerDependencies": {"@alifd/next": "^1.25.0", "react": "^17 || ^18"}, "publishConfig": {"registry": "https://registry.anpm.alibaba-inc.com"}, "license": "MIT", "tnpm": {"mode": "yarn", "lockfile": "enable"}, "repository": "https://code.alibaba-inc.com/op-fe/nr-resource-components.git"}