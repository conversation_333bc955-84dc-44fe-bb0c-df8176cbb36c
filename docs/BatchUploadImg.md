---
sidebar_label: BatchUploadImg
sidebar_position: 1
---

```jsx preview
import { useState } from 'react';
import { Button } from '@alifd/next';
import '@alife/theme-nr-op/dist/next.css';
import { BatchUploadImg } from '@ali/nr-resource-components';

const defaultValue = [
  {
    url: 'https://img.alicdn.com/imgextra/i1/2031564/O1CN011NQMQ7n8o3kWNbh_!!2031564-2-newretailxdt.png',
    name: '150x150.png',
    uid: 'https://img.alicdn.com/imgextra/i1/2031564/O1CN011NQMQ7n8o3kWNbh_!!2031564-2-newretailxdt.png150x150.png',
  },
  {
    url: 'https://img.alicdn.com/imgextra/i3/2031564/O1CN011NQMQ9C5dxNAaDH_!!2031564-2-newretailxdt.png',
    name: '150x150.png',
    uid: 'https://img.alicdn.com/imgextra/i3/2031564/O1CN011NQMQ9C5dxNAaDH_!!2031564-2-newretailxdt.png150x150.png',
  },
  {
    uid: 'https://img.alicdn.com/imgextra/i1/2031564/O1CN011NQMQ3KVkDcPIN0_!!2031564-2-newretailxdt.png150x150.png',
    name: '150x150.png',
    url: 'https://img.alicdn.com/imgextra/i1/2031564/O1CN011NQMQ3KVkDcPIN0_!!2031564-2-newretailxdt.png',
  },
  {
    uid: 'https://img.alicdn.com/imgextra/i4/2031564/O1CN011NQMQ3KVkE2eC6R_!!2031564-2-newretailxdt.png150x150.png',
    name: '150x150.png',
    url: 'https://img.alicdn.com/imgextra/i4/2031564/O1CN011NQMQ3KVkE2eC6R_!!2031564-2-newretailxdt.png',
  },
  {
    uid: 'https://img.alicdn.com/imgextra/i2/2031564/O1CN011NQMQ5kRnhvP00C_!!2031564-2-newretailxdt.png150x150.png',
    name: '150x150.png',
    url: 'https://img.alicdn.com/imgextra/i2/2031564/O1CN011NQMQ5kRnhvP00C_!!2031564-2-newretailxdt.png',
  },
  {
    uid: 'https://img.alicdn.com/imgextra/i1/2031564/O1CN011NQMQ3KNURNdD2b_!!2031564-2-newretailxdt.png150x150.png',
    name: '150x150.png',
    url: 'https://img.alicdn.com/imgextra/i1/2031564/O1CN011NQMQ3KNURNdD2b_!!2031564-2-newretailxdt.png',
  },
  {
    url: 'https://img.alicdn.com/imgextra/i2/2031564/O1CN011NQMQ555j50Pyxl_!!2031564-2-newretailxdt.png',
    name: '150x150.png',
    uid: 'https://img.alicdn.com/imgextra/i2/2031564/O1CN011NQMQ555j50Pyxl_!!2031564-2-newretailxdt.png150x150.png',
  },
  {
    url: 'https://img.alicdn.com/imgextra/i3/2031564/O1CN011NQMQ8egTcelwwn_!!2031564-2-newretailxdt.png',
    name: '150x150.png',
    uid: 'https://img.alicdn.com/imgextra/i3/2031564/O1CN011NQMQ8egTcelwwn_!!2031564-2-newretailxdt.png150x150.png',
  },
  {
    url: 'https://img.alicdn.com/imgextra/i3/2031564/O1CN011NQMQ2X6XC0d1jh_!!2031564-2-newretailxdt.png',
    name: '150x150.png',
    uid: 'https://img.alicdn.com/imgextra/i3/2031564/O1CN011NQMQ2X6XC0d1jh_!!2031564-2-newretailxdt.png150x150.png',
  },
  {
    url: 'https://img.alicdn.com/imgextra/i3/2031564/O1CN011NQMQ555j7BBEUz_!!2031564-2-newretailxdt.png',
    name: '150x150.png',
    uid: 'https://img.alicdn.com/imgextra/i3/2031564/O1CN011NQMQ555j7BBEUz_!!2031564-2-newretailxdt.png150x150.png',
  },
  {
    url: 'https://img.alicdn.com/imgextra/i1/2031564/O1CN011NQMQ8egTefgch6_!!2031564-2-newretailxdt.png',
    name: '150x150.png',
    uid: 'https://img.alicdn.com/imgextra/i1/2031564/O1CN011NQMQ8egTefgch6_!!2031564-2-newretailxdt.png150x150.png',
  },
  {
    url: 'https://img.alicdn.com/imgextra/i3/2031564/O1CN011NQMQ555j8WdNk5_!!2031564-2-newretailxdt.png',
    name: '150x150.png',
    uid: 'https://img.alicdn.com/imgextra/i3/2031564/O1CN011NQMQ555j8WdNk5_!!2031564-2-newretailxdt.png150x150.png',
  },
  {
    url: 'https://img.alicdn.com/imgextra/i2/2031564/O1CN011NQMQ5T8qPXYCKL_!!2031564-2-newretailxdt.png',
    name: '150x150.png',
    uid: 'https://img.alicdn.com/imgextra/i2/2031564/O1CN011NQMQ5T8qPXYCKL_!!2031564-2-newretailxdt.png150x150.png',
  },
];

export default function App() {
  // const [value, setValue] = useState(defaultValue);
  const [value, setValue] = useState();
  const [isPreview, setIsPreview] = useState(false);
  return (
    <div>
      <BatchUploadImg
        env="pre"
        value={value}
        onChange={(value) => {
          console.log('onChange', value);
          setValue(value);
        }}
        isPreview={isPreview}
        schema={{
          options: {
            width: 893,
            height: 893,
            limit: 10,
          },
        }}
      />
      <Button
        style={{ marginTop: 16 }}
        onClick={() => setIsPreview((val) => !val)}
      >
        切换Preview
      </Button>
    </div>
  );
}
```
