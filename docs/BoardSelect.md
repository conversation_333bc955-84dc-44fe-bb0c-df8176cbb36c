---
sidebar_label: BoardSelect
sidebar_position: 1
---

<!-- use
      "shopMajorCategoryList": {
        "type": "atom",
        "x-ui-widget": "BoardSelect",
        "title": "门店类目:"
      },
 -->

```jsx preview
import { useState } from 'react';
import '@alife/theme-nr-op/dist/next.css';
import { Button } from '@alifd/next';
import { BoardSelect } from '@ali/nr-resource-components';
// const defaultVal = [];
const defaultVal = [
  {
    key: 1,
    boardCreateType: 'algo',
    boardType: '1',
    boardId: '121191',
    boardWeight: 258,
  },
  {
    boardCreateType: 'manual',
    boardId: 1000008781,
    boardType: '2',
    key: 145,
    boardWeight: 369,
  },
];

export default function App() {
  const [value, setValue] = useState(defaultVal);
  const [isPreview, setIsPreview] = useState(true);

  return (
    <div>
      <BoardSelect
        env="pre"
        isPreview={isPreview}
        schema={{
          options: {
            limit: 4,
          },
        }}
        value={value}
        onChange={(value) => {
          console.log('value======onChange', value);
          setValue(value);
        }}
      />
      <Button onClick={() => setIsPreview((val) => !val)}>切换Preview</Button>
    </div>
  );
}
```
