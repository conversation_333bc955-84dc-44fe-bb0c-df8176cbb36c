---
sidebar_label: BrandSettingList
sidebar_position: 1
---

<!-- use 资源位 100240013
      "pureDatasourceList": {
        "type": "atom",
        "x-ui-widget": "BrandSettingList",
        "title": "品牌投放内容",
        "options": {
          "limit": 30,
          "imgOptions": {
            "width": 892,
            "height": 892,
            "maxSize": 500,
            "accept": "png,jpeg,jpg,apng,gif"
          }
        }
      }
 -->

```jsx preview
import { useState } from 'react';
import '@alife/theme-nr-op/dist/next.css';
import { Button } from '@alifd/next';
import { BrandSettingList } from '@ali/nr-resource-components';
const defaultVal = [
  {
    brandName: '11',
    imageUrl:
      'https://img.alicdn.com/imgextra/i4/2031564/O1CN01wlWiys1NQMStVyh6B_!!2031564-0-newretailxdt.jpg',
    rightId: 1452151235,
    key: 154,
    content: '112',
  },
  {
    brandName: '22',
    imageUrl:
      'https://img.alicdn.com/imgextra/i3/2031564/O1CN01c9oMCG1NQMSwjXnjZ_!!2031564-0-newretailxdt.jpg',
    rightId: 57654567,
    key: 184,
    content: '223',
  },
  {
    brandName: '33',
    imageUrl:
      'https://img.alicdn.com/imgextra/i4/2031564/O1CN01PFrlOf1NQMStVyh6r_!!2031564-0-newretailxdt.jpg',
    rightId: 5125668,
    key: 227,
    content: '334',
  },
];

export default function App() {
  // const [value, setValue] = useState([]);
  const [value, setValue] = useState(defaultVal);
  const [isPreview, setIsPreview] = useState(true);

  return (
    <div>
      <BrandSettingList
        env="pre"
        isPreview={isPreview}
        schema={{
          options: {
            limit: 4,
            activityType: [1],
            showCornerIcon: true,
            imgOptions: {
              width: 150,
              height: 150,
              maxSize: 1024,
              accept: 'png,jpeg,jpg,apng,gif',
            },
          },
        }}
        value={value}
        onChange={(value) => {
          console.log('value======onChange', value);
          setValue(value);
        }}
      />
      <Button onClick={() => setIsPreview((val) => !val)}>切换Preview</Button>
    </div>
  );
}
```
