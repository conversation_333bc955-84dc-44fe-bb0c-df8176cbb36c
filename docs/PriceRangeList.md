---
sidebar_label: PriceRangeList
sidebar_position: 1
---

<!-- use 淘宝闪购 -> 礼品频道 -> 首个金刚价格筛选 (ID:100236005)
      "priceSection": {
        "type": "atom",
        "x-ui-widget": "PriceRangeList",
        "title": "价格区间配置:",
        "options": {
          "minRange": 0,
          "maxRange": 99999,
          "minPriceRangeCount": 4,
          "maxPriceRangeCount": 24,
          "descLimit": 15
        }
      },
 -->

```jsx preview
import { useEffect, useState } from "react";
import "@alife/theme-nr-op/dist/next.css";
import { Button } from "@alifd/next";
import { PriceRangeList } from "@ali/nr-resource-components";

const defaultVal = [
  {
    startYuan: 1,
    endYuan: 2,
    text: "test2",
  },
  {
    startYuan: 2,
    endYuan: 3,
    text: "test3",
  },
  {
    startYuan: 3,
    endYuan: 4,
    text: "test4",
  },
  {
    startYuan: 4,
    endYuan: 5,
    text: "test",
  },
];

export default function App() {
  const [value, setValue] = useState(defaultVal);
  const [isPreview, setIsPreview] = useState(false);

  useEffect(() => {
    console.log("props======value", value);
  }, [value]);

  return (
    <div>
      <PriceRangeList
        env="pre"
        isPreview={isPreview}
        value={value}
        onChange={(value) => setValue(value)}
      />
      <Button
        style={{ marginTop: 10 }}
        onClick={() => setIsPreview((val) => !val)}
      >
        切换Preview
      </Button>
    </div>
  );
}
```
