---
sidebar_label: GoodsCategorySelect
sidebar_position: 1
---

<!-- use
      "ext_goodCatList": {
        "type": "atom",
        "x-ui-widget": "GoodsCategorySelect",
        "title": "商品类目:"
        "options": {
          "limit": 3,
          "placeholder": "请选择商品类目",
          "multiple": true
        }
      },
 -->

```jsx preview
import { useState } from 'react';
import '@alife/theme-nr-op/dist/next.css';
import { Button } from '@alifd/next';
import { GoodsCategorySelect } from '@ali/nr-resource-components';
const defaultVal = [
  {
    value: '201230206',
    label: '水果',
    level: 1,
  },
];
// const defaultVal = ['1643', '1418', '1466'];
// const defaultVal = ['275', '306', '314', '322', '1643', '1418', '1466'];

export default function App() {
  const [value, setValue] = useState(defaultVal);
  const [isPreview, setIsPreview] = useState(false);

  console.log('value======', value);

  return (
    <div>
      <GoodsCategorySelect
        env="pre"
        isPreview={isPreview}
        value={value}
        schema={{
          options: {
            limit: 3,
            placeholder: '请选择商品类目',
            multiple: true,
          },
        }}
        onChange={(value) => setValue(value)}
      />
      <Button
        style={{ marginTop: 10 }}
        onClick={() => setIsPreview((val) => !val)}
      >
        切换Preview
      </Button>
    </div>
  );
}
```
