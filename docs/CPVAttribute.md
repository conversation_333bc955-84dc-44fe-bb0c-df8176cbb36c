---
sidebar_label: CPVAttribute
sidebar_position: 1
---

<!-- use 淘宝闪购 -> 礼品频道 -> 首个金刚商品属性筛选（仅鲜花） (ID:100236006)
      "cpvList": {
        "type": "atom",
        "x-ui-widget": "CPVAttribute",
        "title": "cpv属性:",
        "options": {
          "minCpvValueCount": 4,
          "maxCpvValueCount": 24
        }
      },
 -->

```jsx preview
import { useEffect, useState } from "react";
import "@alife/theme-nr-op/dist/next.css";
import { Button } from "@alifd/next";
import { CPVAttribute } from "@ali/nr-resource-components";

const defaultVal = "{\"tagKeyId\":\"1331\",\"tagValueList\":[{\"tagValueId\":\"4816726\",\"tagValueName\":\"水果\"},{\"tagValueId\":\"4817487\",\"tagValueName\":\"白玫瑰\"}],\"tagKeyName\":\"主花材\"}"
;

export default function App() {
  const [value, setValue] = useState(defaultVal);
  const [isPreview, setIsPreview] = useState(false);

  useEffect(() => {
    console.log("props======value", value);
  }, [value]);

  return (
    <div>
      <CPVAttribute
        env="pre"
        isPreview={isPreview}
        value={value}
        onChange={(value) => setValue(value)}
        schema={{
          options: {
            minCpvValueCount: 4,
            maxCpvValueCount: 24,
          },
        }}
      />
      <Button
        style={{ marginTop: 10 }}
        onClick={() => setIsPreview((val) => !val)}
      >
        切换Preview
      </Button>
    </div>
  );
}
```
