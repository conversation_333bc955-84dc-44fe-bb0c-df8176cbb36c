---
sidebar_label: FixedPitList
sidebar_position: 1
---

<!-- use
  参考资源位：全能超市大店 -> 天天好价 -> 友好价分类（100231003）
      "dataConfigList": {
        "type": "atom",
        "x-ui-widget": "FixedPitList",
        "title": "定坑配置（1-10）:",
        "options": {
          "limit": 10,
          "activityType": [1],
          "imgOptions": {
            "width": 123,
            "height":113,
            "maxSize": 500,
            "accept": "png,jpeg,jpg,apng,gif"
          }
        }
      }
 -->

```jsx preview
import { useState } from 'react';
import '@alife/theme-nr-op/dist/next.css';
import { Button } from '@alifd/next';
import { FixedPitList } from '@ali/nr-resource-components';
const defaultVal = [
  {
    activityId: 310778,
    cornerIconUrl:
      'https://img.alicdn.com/imgextra/i1/1195859/O1CN01Xtda821t9TY7wmi2A_!!1195859-2-newretailxdt.png',
    recallSize: 3,
    activityType: '1',
    key: 20,
  },
];

export default function App() {
  const [value, setValue] = useState(defaultVal);
  const [isPreview, setIsPreview] = useState(false);

  return (
    <div>
      <FixedPitList
        env="pre"
        isPreview={isPreview}
        schema={{
          options: {
            limit: 4,
            activityType: [1],
            showCornerIcon: true,
            imgOptions: {
              width: 86,
              height: 36,
              maxSize: 1024,
              accept: 'png,jpeg,jpg,apng,gif',
            },
          },
        }}
        value={value}
        onChange={(value) => {
          console.log('value======onChange', value);
          setValue(value);
        }}
      />
      <Button onClick={() => setIsPreview((val) => !val)}>切换Preview</Button>
    </div>
  );
}
```
