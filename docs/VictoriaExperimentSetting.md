---
sidebar_label: VictoriaExperimentSetting
sidebar_position: 1
---

```jsx preview
import { useState } from "react";
import "@alife/theme-nr-op/dist/next.css";
import { VictoriaExperimentSetting } from "@ali/nr-resource-components";

export default function App() {
  const [value, setValue] = useState();
  return (
    <VictoriaExperimentSetting
      env="pre"
      value={value}
      onChange={(value) => {
        console.log("onChange", value);
        setValue(value);
      }}
    />
  );
}
```
