---
sidebar_label: StoreCategorySelect
sidebar_position: 1
---

<!-- use
      "shopMajorCategoryList": {
        "type": "atom",
        "x-ui-widget": "StoreCategorySelect",
        "title": "门店类目:"
      },
 -->

```jsx preview
import { useState } from 'react';
import '@alife/theme-nr-op/dist/next.css';
import { Button } from '@alifd/next';
import { StoreCategorySelect } from '@ali/nr-resource-components';
const defaultVal = ['1643', '1418', '1466'];
// const defaultVal = ['275', '306', '314', '322', '1643', '1418', '1466'];

export default function App() {
  const [value, setValue] = useState(defaultVal);
  const [isPreview, setIsPreview] = useState(false);

  return (
    <div>
      <StoreCategorySelect
        env="pre"
        isPreview={isPreview}
        value={value}
        onChange={(value) => setValue(value)}
      />
      <Button
        style={{ marginTop: 10 }}
        onClick={() => setIsPreview((val) => !val)}
      >
        切换Preview
      </Button>
    </div>
  );
}
```
