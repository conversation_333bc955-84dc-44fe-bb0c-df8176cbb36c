---
sidebar_label: UploadFile
sidebar_position: 1
---

<!-- use
      "ext_skuListFile": {
        "type": "atom",
        "x-ui-widget": "UploadFile",
        "title": "",
        "options": {
          "templateUrl": "www.baidu.com"
        }
      },
 -->

```jsx preview
import { useState } from "react";
import "@alife/theme-nr-op/dist/next.css";
import { Button } from "@alifd/next";
import { UploadFile } from "@ali/nr-resource-components";
// const defaultVal = '';
const defaultVal = JSON.stringify({
  fileName: "sku_id上传模板.xlsx",
  ossFileUrl:
    "https://newretail-pick-admin-zbprod-zb1-oss-2.oss-cn-zhangjiakou.aliyuncs.com/delivery_platform/1741747452257_%E3%80%90%E9%80%89%E6%8A%95%E4%BE%A7%E3%80%91sku_id%E4%B8%8A%E4%BC%A0%E6%A8%A1%E7%89%88.xlsx?Expires=2000947452&OSSAccessKeyId=LTAImLkY3owlmCS4&Signature=EH7ANkDiVDWHVmA%2FOV16niYu1SE%3D",
});

const templateUrl =
  "https://files.alicdn.com/tpsservice/8be50374b1896c842fcc2bf56e42d436.xlsx";

export default function App() {
  const [value, setValue] = useState(defaultVal);
  const [isPreview, setIsPreview] = useState(false);

  console.log("value======", value);

  return (
    <div>
      <UploadFile
        env="pre"
        isPreview={isPreview}
        value={value}
        schema={{
          options: {
            templateUrl,
          },
        }}
        onChange={(value) => setValue(value)}
      />
      <Button
        style={{ marginTop: 10 }}
        onClick={() => setIsPreview((val) => !val)}
      >
        切换Preview
      </Button>
    </div>
  );
}
```
