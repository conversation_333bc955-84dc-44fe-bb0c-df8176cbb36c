---
sidebar_label: UploadFileAndTextarea
sidebar_position: 1
---

<!-- use by 全能超市大店 - 加购面板 - 加购面板sku角标 (ID:100232001)
      "ext_skuListFile": {
        "type": "atom",
        "x-ui-widget": "UploadFileAndTextarea",
        "title": "",
        "options": {
          "templateUrl": "https://files.alicdn.com/tpsservice/8be50374b1896c842fcc2bf56e42d436.xlsx",
          "placeholder": "最多输入500个sku，sku之间用英文逗号分隔",
          "maxCount": 500,
        }
      },
 -->

```jsx preview
import { useState } from "react";
import "@alife/theme-nr-op/dist/next.css";
import { Button } from "@alifd/next";
import { UploadFileAndTextarea } from "@ali/nr-resource-components";
const defaultVal = '{"type":"text","value":"1231414"}';
// const defaultVal = JSON.stringify({
//   type: "file",
//   value: '{"fileName":"1741922343002_【选投侧】sku_id上传模版.xlsx","ossFileUrl":"https://newretail-pick-admin-zbprod-zb1-oss-2.oss-cn-zhangjiakou.aliyuncs.com/delivery_platform/1741922343002_%E3%80%90%E9%80%89%E6%8A%95%E4%BE%A7%E3%80%91sku_id%E4%B8%8A%E4%BC%A0%E6%A8%A1%E7%89%88.xlsx?Expires=2001122343&OSSAccessKeyId=LTAImLkY3owlmCS4&Signature=9oEtJxMq%2BxhBRVslkY%2F7QDJyhJ4%3D"}',
// });

const templateUrl =
  "https://files.alicdn.com/tpsservice/8be50374b1896c842fcc2bf56e42d436.xlsx";

export default function App() {
  const [value, setValue] = useState(defaultVal);
  const [isPreview, setIsPreview] = useState(false);

  console.log("value======", value);

  return (
    <div>
      <UploadFileAndTextarea
        env="pre"
        isPreview={isPreview}
        value={value}
        schema={{
          options: {
            templateUrl,
            placeholder: "最多输入500个sku，sku之间用英文逗号分隔",
            maxCount: 5,
            // maxLength: 100,
          },
        }}
        onChange={(value) => setValue(value)}
      />
      <Button
        style={{ marginTop: 10 }}
        onClick={() => setIsPreview((val) => !val)}
      >
        切换Preview
      </Button>
    </div>
  );
}
```
