---
sidebar_label: AiThemeCard
sidebar_position: 1
---

<!-- use
      "ext_skuListFile": {
        "type": "atom",
        "x-ui-widget": "AiThemeCard",
        "title": "",
        "options": {
          "balloonText": "最多展示10个品类",
          "isRequired": true,
          "showBalloon": true
        }
      },
 -->

```jsx preview
import { useState } from "react";
import "@alife/theme-nr-op/dist/next.css";
import { Button } from "@alifd/next";
import { AiThemeCard } from "@ali/nr-resource-components";
// const defaultVal = ''
const defaultVal = "{\"cardId\":\"202504207\",\"cateInfo\":\"[{\\\"cate3Id\\\":\\\"201231040\\\",\\\"cate3Name\\\":\\\"永生花\\\"},{\\\"cate3Id\\\":\\\"201222961\\\",\\\"cate3Name\\\":\\\"围巾/丝巾/披肩\\\"},{\\\"cate3Id\\\":\\\"201218249\\\",\\\"cate3Name\\\":\\\"蓝牙音箱\\\"}]\",\"themeType\":\"时光里的温柔\",\"title\":\"定格专属浪漫\"}"

export default function App() {
  const [value, setValue] = useState(defaultVal);
  const [isPreview, setIsPreview] = useState(true);

  console.log("value======", value);

  return (
    <div>
      <AiThemeCard
        env="pre"
        isPreview={isPreview}
        value={value}
        schema={{
          options: {
            balloonText: "最多展示10个品类",
            isRequired: true,
            showBalloon: true,
          },
        }}
        onChange={(value) => setValue(value)}
      />
      <Button
        style={{ marginTop: 10 }}
        onClick={() => setIsPreview((val) => !val)}
      >
        切换Preview
      </Button>
    </div>
  );
}
```
