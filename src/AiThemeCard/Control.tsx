import "./Control.scss";

import { useEffect, useMemo, useState } from "react";
import { Balloon, Icon, Select } from "@alifd/next";

import { request, Response } from "pkg/request";
import { CardItem, Props } from "./types";
import CateList from "./CateList";

// AI主题卡 创建/编辑, value: CardItem;
export default function Control({ env, schema, value, onChange }: Props) {
  const schemaOptions = schema?.options || {};
  const [themeCardTypeDataSource, setThemeCardTypeDataSource] = useState<
    string[]
  >([]);
  const [themeCardType, setThemeCardType] = useState<string>();
  const [themeCardDataSource, setThemeCardDataSource] = useState([]);

  const valueObj = useMemo(() => {
    if (typeof value === "string") {
      return JSON.parse(value as string);
    } else {
      return value;
    }
  }, [value]);

  useEffect(() => {
    if (value && typeof value === "string") {
      const _value = JSON.parse(value as string);
      setThemeCardDataSource([
        { ..._value, label: valueObj.title, value: valueObj.cardId },
      ]);
    }
  }, [value]);

  useEffect(() => {
    request<Response<{ themeCardType: string[] }>>({
      env,
      method: "POST",
      api: "/api/common/queryBaseByKeywords",
      data: {
        keywords: ["giftThemeCardType"],
      },
    }).then((resp) => {
      setThemeCardTypeDataSource(resp.data?.themeCardType || []);
    });
  }, [env]);

  const onThemeCardTypeChange = (v: string) => {
    setThemeCardType(v);
    onChange(undefined);
    request<Response<CardItem[]>>({
      env,
      method: "POST",
      api: "/api/common/queryThemeCardList",
      data: {
        themeCardType: v,
      },
    }).then((res) => {
      const data = res?.data?.map((_item) => {
        return {
          ..._item,
          label: _item.title,
          value: _item.cardId,
        };
      });
      setThemeCardDataSource(data);
    });
  };

  const onThemeCardChange = (
    cardId: string,
    actionType: string,
    record: CardItem
  ) => {
    onChange({
      cardId: record.cardId,
      themeType: record.themeType,
      title: record.title,
      cateInfo: record.cateInfo,
    });
  };
  return (
    <div className="nr-theme-card-select">
      <div className="tcs-theme-card-item">
        <div className="theme-card-select-label">
          {schemaOptions?.isRequired ? (
            <span style={{ color: "red" }}>*</span>
          ) : null}{" "}
          请选择主题卡类型：{" "}
        </div>
        <Select
          className="theme-card-select-content"
          dataSource={themeCardTypeDataSource}
          hasClear
          placeholder={"请选择主题卡类型"}
          showSearch
          style={{ width: "100%" }}
          value={themeCardType || valueObj?.themeType}
          onChange={onThemeCardTypeChange}
        />
      </div>

      <div className="tcs-theme-card-item">
        <div className="theme-card-select-label">
          {schemaOptions?.isRequired ? (
            <span style={{ color: "red" }}>*</span>
          ) : null}{" "}
          请选择主题卡：{" "}
        </div>
        <Select
          className="theme-card-select-content"
          dataSource={themeCardDataSource}
          accessKey="cardId"
          hasClear
          placeholder={"请选择主题卡"}
          showSearch
          value={valueObj?.cardId}
          onChange={onThemeCardChange}
        />
      </div>

      {valueObj?.cateInfo ? (
        <div className="tcs-theme-card-item">
          <div className="theme-card-select-label">
            <div className="theme-card-select-label">
              关联品类
              {schemaOptions?.showBalloon ? (
                <Balloon
                  v2
                  closable={false}
                  trigger={
                    <Icon
                      style={{ color: "#999", fontSize: 16, marginLeft: 4 }}
                      type="help"
                      size="xs"
                    />
                  }
                >
                  {schemaOptions.balloonText || "最多展示10个品类"}
                </Balloon>
              ) : null}
              ：{" "}
            </div>{" "}
          </div>
          <div className="theme-card-select-content">
            <CateList dataJson={valueObj?.cateInfo} />
          </div>
        </div>
      ) : null}
    </div>
  );
}
