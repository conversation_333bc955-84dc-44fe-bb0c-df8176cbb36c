import React from "react";

import "./Control.scss";
import { Props } from "./types";
import CateList from "./CateList";
import { Balloon, Icon } from "@alifd/next";

// 主题卡详情
export default function Preview({ schema, value: valueStr }: Props) {
  const schemaOptions = schema?.options || {};
  const value =
    valueStr && typeof valueStr === "string"
      ? JSON.parse(valueStr as string)
      : valueStr;

  if (!value) {
    return null;
  }

  return (
    <div className="nr-theme-card-select">
      <div className="tcs-theme-card-item">
        <div className="theme-card-select-label">主题卡类型： </div>
        <div className="theme-card-select-content">{value?.themeType}</div>
      </div>

      <div className="tcs-theme-card-item">
        <div className="theme-card-select-label">主题卡： </div>
        <div className="theme-card-select-content">{value?.title}</div>
      </div>

      <div className="tcs-theme-card-item">
        <div className="theme-card-select-label">
          关联品类
          {schemaOptions?.showBalloon ? (
            <Balloon
              v2
              closable={false}
              trigger={
                <Icon
                  style={{ color: "#999", fontSize: 16, marginLeft: 4 }}
                  type="help"
                  size="xs"
                />
              }
            >
              {schemaOptions.balloonText || "最多展示10个品类"}
            </Balloon>
          ) : null}
          ：{" "}
        </div>

        <div className="theme-card-select-content">
          <CateList dataJson={value?.cateInfo} />
        </div>
      </div>
    </div>
  );
}
