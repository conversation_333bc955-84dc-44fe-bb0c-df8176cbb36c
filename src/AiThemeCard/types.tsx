export interface CardItem {
  cardId: number;
  themeType: string;
  title: string;
  cateInfo: string;
}

export interface Value extends CardItem {}

export interface Props {
  env: "prod" | "pre";
  isPreview?: boolean;
  schema?: {
    options: {
      isRequired?: boolean;
      showBalloon?: boolean;
      balloonText?: string;
    };
  };
  value?: Value | string;

  onChange?: (value?: Value) => void;
}
