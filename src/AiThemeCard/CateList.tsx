import React, { useMemo } from "react";
import "./Control.scss";

interface Props {
  dataJson: string;
}

// 主题卡关联的品类
export const CateList: React.FC<Props> = ({ dataJson }) => {
  const data: { cate3Id: number; cate3Name: string }[] = useMemo(() => {
    return JSON.parse(dataJson) || [];
  }, [dataJson]);

  return (
    <div className="tsc-cate-list">
      {data?.slice(0, 10)?.map((_item) => {
        return (
          <div className="tsc-cate-item">
            {_item.cate3Name}（{_item.cate3Id}）
          </div>
        );
      })}
    </div>
  );
};

export default CateList;
