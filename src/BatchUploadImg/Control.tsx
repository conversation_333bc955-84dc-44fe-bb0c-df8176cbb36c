import React from 'react';

import './index.scss';

import { Props } from './types';
import { Message, Upload } from '@alifd/next';
import { request, Response } from 'pkg/request';
import { parseUiValidate } from './utils';

const defaultAccept =
  'image/png, image/jpg, image/jpeg, image/apng, image/gif, image/bmp';

const Control: React.FC<Props> = ({ env, isPreview, schema, value = [], onChange }) => {
  const schemaOptions = schema.options || {};
  const validateInfo = parseUiValidate(schemaOptions);

  const onUpdate = async (option: any) => {
    let aborted = false;
    const reader = new FileReader();
    reader.onload = () => {
      const base64 = reader?.result as string;

      const _base64 = base64?.replace(/^data\:.*?;base64,/, '');
      request<
        Response<string> & { code: string; success: boolean; msg: string }
      >({
        env,
        method: 'POST',
        api: '/api/pic/uploadPic',
        data: {
          userId: '1',
          base64: _base64,
          name: option?.file?.name,
        },
      })
        .then((res) => {
          if (res.success && res.code === '200') {
            if (!aborted) {
              option.onSuccess({ success: true, url: res.data });
            }
            Message.show('上传成功');
          } else {
            Message.error(`上传失败，${res.msg || '未知错误'}`);
          }
        })
        .catch((e) => {
          Message.error(
            `上传失败，${
              e.error && (e?.error?.message || e?.message || '未知错误')
            }`
          );
          if (!aborted) {
            option.onError(e);
          }
        });
    };
    reader.readAsDataURL(option.file);
    return {
      abort() {
        aborted = true;
      },
    };
  };

  const validateImg = ({
    name,
    src,
    size,
    type,
  }: {
    name: string;
    src: string;
    size?: number;
    type?: string;
  }) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.src = src;
      let hasError = false;

      img.onload = function () {
        const v = { ...validateInfo };
        if (!v) hasError = false;

        const { width, height } = img;
        if (v.width && Math.abs(v.width - width) > 1) {
          Message.error(
            `图片（${name}）宽度需为${v.width}px，目前宽度${width}px`
          );
          hasError = true;
        }
        if (v.maxWidth && v.maxWidth < width) {
          Message.error(`图片（${name}）宽度需小于${v.maxWidth}px`);
          hasError = true;
        }
        if (v.minWidth && v.minWidth > width) {
          Message.error(`图片（${name}）宽度需大于${v.minWidth}px`);
          hasError = true;
        }
        if (v.height && Math.abs(v.height - height) > 1) {
          Message.error(
            `图片（${name}）高度需为${v.height}px，目前高度${height}px`
          );
          hasError = true;
        }
        if (v.maxHeight && v.maxHeight < height) {
          Message.error(`图片（${name}）高度需小于${v.maxHeight}px`);
          hasError = true;
        }
        if (v.minHeight && v.minHeight > height) {
          Message.error(`图片（${name}）高度需大于${v.minHeight}px`);
          hasError = true;
        }
        if (v.maxSize && v.maxSize * 1024 < size) {
          Message.error(`图片（${name}）体积需小于${v.maxSize}kb`);
          hasError = true;
        }
        if (v.minSize && v.minSize * 1024 > size) {
          Message.error(`图片（${name}）体积需大于${v.minSize}kb`);
          hasError = true;
        }
        if (v.accept) {
          const imageType = type.replace('image/', '');
          const availableTypes = v.accept
            .split(',')
            .map((item = '') => item.trim());
          if (availableTypes.indexOf('jpg') >= 0) availableTypes.push('jpeg');
          if (availableTypes.indexOf(imageType) === -1) {
            Message.error(
              `图片（${name}）格式需为${v.accept}，检测到格式为${imageType}`
            );
            hasError = true;
          }
        }

        resolve({
          valid: !hasError,
        });
      };

      img.onerror = function () {
        reject(new Error(`Failed to load image ${src}`));
      };
    });
  };

  const beforeUpload = async (file: File & { uid: string }) => {
    const reader = new FileReader();

    const getCanUpload = () => {
      return new Promise((resolve) => {
        reader.onload = async () => {
          // const v = { ...validateInfo };
          // if (!v) return true;
          const img = new Image();
          img.src = reader?.result as string;

          const validRes = (await validateImg({
            name: file.name,
            src: reader?.result as string,
            size: file.size,
            type: file.type,
          })) as { valid: boolean };

          resolve(validRes.valid);
        };
        reader.readAsDataURL(file);
      });
    };

    // 异步转同步
    const canUpload = await getCanUpload();
    // return true 允许上传，false不允许上传
    return canUpload;
  };

  const _onChange = (v: (File & { uid: string; url: string })[]) => {
    const _valueList = v
      ?.filter((item) => item.url)
      ?.map((item) => {
        return {
          url: item.url,
          name: item.name,
          uid: item.uid,
        };
      })
      .filter(Boolean);
    onChange(_valueList);
  };
  return (
    <div className="batch_upload_img">
      <Upload.Card
        className="alsc-form-item-uploader"
        accept={defaultAccept}
        disabled={isPreview}
        limit={schemaOptions?.limit || 1}
        multiple
        request={onUpdate}
        beforeUpload={beforeUpload}
        value={value}
        onChange={_onChange}
      />

      {validateInfo && (
        <div className="restriction_text">
          <div className="restrict_item">
            上传尺寸：
            {[
              validateInfo.width && `宽${validateInfo.width}px`,
              validateInfo.minWidth && `宽>${validateInfo.minWidth}px`,
              validateInfo.maxWidth && `宽<${validateInfo.maxWidth}px`,
              validateInfo.height && `高${validateInfo.height}px`,
              validateInfo.minHeight && `高>${validateInfo.minHeight}px`,
              validateInfo.maxHeight && `高<${validateInfo.maxHeight}px`,
            ]
              ?.filter((x) => !!x)
              ?.join(', ')}
          </div>
          <div className="restrict_item">
            上传格式：{validateInfo.accept}
          </div>
          {(validateInfo.minSize || validateInfo.maxSize) && (
            <div className="restrict_item">
              图片大小：
              {[
                validateInfo.minSize && `体积>${validateInfo.minSize}kb`,
                validateInfo.maxSize && `体积<${validateInfo.maxSize}kb`,
              ]
                .filter((x) => !!x)
                .join(', ')}
            </div>
          )}
          {schemaOptions.limit && (
            <div className="restrict_item">
              最多上传：{schemaOptions.limit} 张图片
            </div>
          )}
        </div>
      )}
    </div>
  );
};
export default Control;
