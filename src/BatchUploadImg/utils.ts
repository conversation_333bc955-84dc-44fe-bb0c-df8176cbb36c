import { SchemaOptions } from 'BatchUploadImg/types';

export function parseUiValidate(option: SchemaOptions) {
  if (!option) return option;
  const { minSize, maxSize, accept, placeholder } = option;
  const result = {} as SchemaOptions;
  const processDimension = (
    name: 'width' | 'height',
    minName: 'minWidth' | 'minHeight',
    maxName: 'maxWidth' | 'maxHeight'
  ) => {
    if (option[name]) {
      result[name] = option[name];
    } else if (
      option[minName] &&
      option[maxName] &&
      option[minName] < option[maxName]
    ) {
      result[minName] = option[minName];
      result[maxName] = option[maxName];
    } else if (option[maxName]) {
      result[maxName] = option[maxName];
    } else if (option[minName]) {
      result[minName] = option[minName];
    }
  };
  processDimension('width', 'minWidth', 'maxWidth');
  processDimension('height', 'minHeight', 'maxHeight');
  
  if (minSize && maxSize && minSize < maxSize) {
    result.minSize = minSize;
    result.maxSize = maxSize;
  } else if (maxSize) {
    result.maxSize = maxSize;
  } else if (minSize) {
    result.minSize = minSize;
  }
  if (typeof accept === 'string') {
    result.accept = accept;
  }
  if (typeof placeholder === 'string') {
    result.placeholder = placeholder;
  }
  if (Object.keys(result).length === 0) return undefined;
  return result;
}
