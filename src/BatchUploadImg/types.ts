export interface Value {
  url: string;
  name: string;
  uid: string;
}

export interface SchemaOptions {
  patternMsg?: string; // 正则表达式错误的提示
  limit?: number;
  minSize?: number;
  maxSize?: number;
  accept?: string;
  maxWidth?: number;
  minWidth?: number;
  height?: number;
  maxHeight?: number;
  minHeight?: number;
  placeholder?: string;
  width?: number;
}

export interface Props {
  env: 'prod' | 'pre';
  isPreview?: boolean;
  value?: Value[];
  schema: {
    options: SchemaOptions;
    pattern?: string; // 正则表达式
    title: string;
    type: 'atomic';
    'x-ui-widget': string;
  };

  onChange?: (value: Value[]) => void;
}
