import "./Control.scss";

import { useEffect, useState } from "react";
import { CascaderSelect, Message } from "@alifd/next";

import { request, Response } from "pkg/request";
import { Item, Props } from "./types";
import { baseURL } from "./common";

// 门店类目 创建/编辑，Value: string[];
export default function Control({ env, schema, value, onChange }: Props) {
  const schemaOptions = schema?.options || {};
  const [dataSource, setDataSource] = useState<Item[]>([]);

  useEffect(() => {
    request<Response<{ data: Item[] }>>({
      baseURL,
      env,
      method: "GET",
      api: "/api/v2/config/queryAllSkuCategory",
    }).then((resp) => {
      const _dataSource = resp.data?.data?.map((data) => {
        return {
          ...data,
          value: data.value.toString(),
          children: data.children.map((subItem) => ({
            ...subItem,
            value: subItem.value.toString(),
            children: subItem.children.map((thirdItem) => ({
              ...thirdItem,
              value: thirdItem.value.toString(),
            })),
          })),
        };
      });
      setDataSource(_dataSource);
    });
  }, [env]);

  /**
   * @function _onChange 选择回调
   * @param value 值
   * @param data 值对应的对象
   */

  const onCategoryChange = (_value: string | Array<string>, data: any[]) => {
    if (0 < schemaOptions?.limit) {
      if (data.length > schemaOptions?.limit) {
        Message.error(`最多选择${schemaOptions?.limit}个类目`);
        return;
      }
    }
    const hasSelectedDisplay = data.map((item) => {
      const { value: itemValue, label, pos } = item;
      return {
        value: itemValue,
        label,
        level: pos.split("-").length - 1,
      };
    });

    onChange && onChange(hasSelectedDisplay);
  };
  return (
    <div className="nr-goods-category-select">
      <CascaderSelect
        expandTriggerType={"hover"}
        dataSource={dataSource}
        hasClear={true}
        multiple={true}
        listStyle={{ minWidth: 200 }}
        placeholder={schemaOptions?.placeholder || "请选择商品类目"}
        showSearch
        style={{ width: "100%" }}
        value={value?.map((item) => item.value)}
        onChange={onCategoryChange}
      />
    </div>
  );
}
