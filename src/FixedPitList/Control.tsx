import './styles.scss';

import { useEffect, useState } from 'react';
import { Button } from '@alifd/next';

import { ActivityType, Props, Value } from './types';
import PitItem from './components/PitItem';
import { genId } from './utils';

// 门店类目 创建/编辑，Value: string[];
export default function Control({ env, schema, value, onChange }: Props) {
  const schemaOptions = schema?.options || {};
  const [pitList, setBoardList] = useState<Value[]>(
    value?.length > 0
      ? value.map((item) => ({ ...item, key: genId() }))
      : [{ key: genId(), activityType: ActivityType.COMMODITY } as Value]
  );

  useEffect(() => {
    // 只同步有效数据 —— 有 activityId 的才被认为有效数据
    const validList = pitList.filter((item) => {
      return item.activityId && item.key;
    });

    // undefined 用于表单校验
    onChange(validList?.length > 0 ? validList : undefined);
  }, [pitList]);

  return (
    <div className="nr-pit-select">
      {pitList?.map((item, index) => (
        <PitItem
          key={item.key}
          currentNum={index + 1}
          env={env}
          schemaOptions={schemaOptions}
          showDownBtn={index < pitList.length - 1}
          showRemoveBtn={true}
          showUpBtn={index > 0}
          value={item}
          onChange={(v: Value) => {
            const curIndex = pitList.findIndex((i) => i.key === v.key);
            if (curIndex === -1) {
              setBoardList([...pitList, v]);
            } else {
              setBoardList([
                ...pitList.slice(0, curIndex),
                v,
                ...pitList.slice(curIndex + 1),
              ]);
            }
          }}
          onItemDown={() => {
            const curIndex = pitList.findIndex((i) => i.key === item.key);
            if (curIndex < pitList.length - 1) {
              const newList = [
                ...pitList.slice(0, curIndex),
                pitList[curIndex + 1],
                pitList[curIndex],
                ...pitList.slice(curIndex + 2),
              ];
              setBoardList(newList);
            }
          }}
          onItemRemove={() => {
            const _value = [...pitList];
            const curIndex = _value.findIndex((i) => i.key === item.key);
            if (curIndex > -1) {
              _value.splice(curIndex, 1);
              setBoardList(_value);
            }
          }}
          onItemUp={() => {
            const curIndex = pitList.findIndex((i) => i.key === item.key);
            if (curIndex > 0) {
              const newList = [
                ...pitList.slice(0, curIndex - 1),
                pitList[curIndex],
                pitList[curIndex - 1],
                ...pitList.slice(curIndex + 1),
              ];
              setBoardList(newList);
            }
          }}
        />
      ))}

      {/* 新增 */}
      {pitList?.length < (schemaOptions?.limit || 1) ? (
        <Button
          onClick={() => {
            setBoardList([
              ...pitList,
              { key: genId(), activityType: ActivityType.COMMODITY } as Value,
            ]);
          }}
          text
          type="primary"
        >
          新增
        </Button>
      ) : null}
    </div>
  );
}
