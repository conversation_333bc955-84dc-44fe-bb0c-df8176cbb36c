import './styles.scss';

import { useState } from 'react';

import { Props, Value } from './types';
import PitItem from './components/PitItem';
import { genId } from './utils';

// 门店类目 详情，Value: string[];
export default function Preview({ env, schema, value }: Props) {
  const [pitList] = useState<Value[]>(
    value?.length > 0 ? value.map((item) => ({ ...item, key: genId() })) : []
  );

  return (
    <div className="nr-pit-select">
      {pitList?.map((item, i) => (
        <PitItem
          key={item.key}
          currentNum={i + 1}
          env={env}
          value={item}
          schemaOptions={schema?.options}
          isPreview={true}
        />
      ))}
    </div>
  );
}
