
import { Message } from '@alifd/next';
import { ImgOptions } from './types';

export const genId = (() => {
  let _id = 0;
  return () => {
    return ++_id;
  };
})();

export function parseUiValidate(option: ImgOptions) {
  if (!option) return option;
  const { minSize, maxSize, accept, placeholder } = option;
  const result = {} as ImgOptions;
  const processDimension = (
    name: 'width' | 'height',
    minName: 'minWidth' | 'minHeight',
    maxName: 'maxWidth' | 'maxHeight'
  ) => {
    if (option[name]) {
      result[name] = option[name];
    } else if (
      option[minName] &&
      option[maxName] &&
      option[minName] < option[maxName]
    ) {
      result[minName] = option[minName];
      result[maxName] = option[maxName];
    } else if (option[maxName]) {
      result[maxName] = option[maxName];
    } else if (option[minName]) {
      result[minName] = option[minName];
    }
  };
  processDimension('width', 'minWidth', 'maxWidth');
  processDimension('height', 'minHeight', 'maxHeight');

  if (minSize && maxSize && minSize < maxSize) {
    result.minSize = minSize;
    result.maxSize = maxSize;
  } else if (maxSize) {
    result.maxSize = maxSize;
  } else if (minSize) {
    result.minSize = minSize;
  }
  if (typeof accept === 'string') {
    result.accept = accept;
  }
  if (typeof placeholder === 'string') {
    result.placeholder = placeholder;
  }
  if (Object.keys(result).length === 0) return undefined;
  return result;
}

const validateImg = ({
  name,
  src,
  size,
  type,
  validateInfo,
}: {
  name: string;
  src: string;
  size?: number;
  type?: string;
  validateInfo: ImgOptions;
}) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = src;
    let hasError = false;

    img.onload = function () {
      const v = { ...validateInfo };
      if (!v) hasError = false;

      const { width, height } = img;
      if (v.width && Math.abs(v.width - width) > 1) {
        Message.error(
          `图片（${name}）宽度需为${v.width}px，目前宽度${width}px`
        );
        hasError = true;
      }
      if (v.maxWidth && v.maxWidth < width) {
        Message.error(`图片（${name}）宽度需小于${v.maxWidth}px`);
        hasError = true;
      }
      if (v.minWidth && v.minWidth > width) {
        Message.error(`图片（${name}）宽度需大于${v.minWidth}px`);
        hasError = true;
      }
      if (v.height && Math.abs(v.height - height) > 1) {
        Message.error(
          `图片（${name}）高度需为${v.height}px，目前高度${height}px`
        );
        hasError = true;
      }
      if (v.maxHeight && v.maxHeight < height) {
        Message.error(`图片（${name}）高度需小于${v.maxHeight}px`);
        hasError = true;
      }
      if (v.minHeight && v.minHeight > height) {
        Message.error(`图片（${name}）高度需大于${v.minHeight}px`);
        hasError = true;
      }
      if (v.maxSize && v.maxSize * 1024 < size) {
        Message.error(`图片（${name}）体积需小于${v.maxSize}kb`);
        hasError = true;
      }
      if (v.minSize && v.minSize * 1024 > size) {
        Message.error(`图片（${name}）体积需大于${v.minSize}kb`);
        hasError = true;
      }
      if (v.accept) {
        const imageType = type.replace('image/', '');
        const availableTypes = v.accept
          .split(',')
          .map((item = '') => item.trim());
        if (availableTypes.indexOf('jpg') >= 0) availableTypes.push('jpeg');
        if (availableTypes.indexOf(imageType) === -1) {
          Message.error(
            `图片（${name}）格式需为${v.accept}，检测到格式为${imageType}`
          );
          hasError = true;
        }
      }

      resolve({
        valid: !hasError,
      });
    };

    img.onerror = function () {
      reject(new Error(`Failed to load image ${src}`));
    };
  });
};
export const beforeUpload = async (
  file: File & { uid: string },
  validateInfo: ImgOptions
) => {
  const reader = new FileReader();

  const getCanUpload = () => {
    return new Promise((resolve) => {
      reader.onload = async () => {
        // const v = { ...validateInfo };
        // if (!v) return true;
        const img = new Image();
        img.src = reader?.result as string;

        const validRes = (await validateImg({
          name: file.name,
          src: reader?.result as string,
          size: file.size,
          type: file.type,
          validateInfo,
        })) as { valid: boolean };

        resolve(validRes.valid);
      };
      reader.readAsDataURL(file);
    });
  };

  // 异步转同步
  const canUpload = await getCanUpload();
  // return true 允许上传，false不允许上传
  return canUpload;
};
