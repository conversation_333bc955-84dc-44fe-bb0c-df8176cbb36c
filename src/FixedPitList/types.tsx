export interface ImgOptions {
  limit?: number;
  minSize?: number;
  maxSize?: number;
  accept?: string;
  maxWidth?: number;
  minWidth?: number;
  height?: number;
  maxHeight?: number;
  minHeight?: number;
  placeholder?: string;
  width?: number;
}

export interface SchemaOptions {
  activityType?: ActivityType[]; // 活动类型
  limit?: number; // 列表最大数量
  activityIdLimit?: number; // 活动ID最大数量 这个应该不需要
  imgOptions?: ImgOptions;  // 图片宽高大小等配置
  showCornerIcon?: boolean; // 是否展示角标
}

export interface Schema {
  options: SchemaOptions;
}

export interface Item {
  value: ActivityType;
  label: string;
  children?: Item[];
}

export enum ActivityType {
  COMMODITY = '1', // "商品"
  STORE = '2', // "门店
}

export interface Value {
  recallSize: number;
  activityType: ActivityType; // 商品、门店
  activityId: number | string;
  cornerIconUrl: string;
  key: number;
}

export interface Props {
  env: 'prod' | 'pre';
  isPreview?: boolean;
  schema: Schema;
  value?: Value[];

  onChange?: (value: Value[] | undefined) => void;
}

export interface Response<T> {
  success: boolean;
  data?: T;
  msg?: string;
}
