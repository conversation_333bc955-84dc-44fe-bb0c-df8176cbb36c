import React, { useMemo, useState } from 'react';
import { Button, Message, NumberPicker, Radio, Upload } from '@alifd/next';
import _ from 'lodash';
import { request, Response } from 'pkg/request';

import styles from './styles.module.scss';

import { ActivityType, Item, SchemaOptions, Value } from '../types';
import { beforeUpload, parseUiValidate } from '../utils';

// 活动类型
const defaultActivityType: Item[] = [
  { label: '商品', value: ActivityType.COMMODITY },
  { label: '门店', value: ActivityType.STORE },
];

const defaultAccept =
  'image/png, image/jpg, image/jpeg, image/apng, image/gif, image/bmp';

const FormItem: React.FC<{
  label: string;
  isRequired?: boolean;
  children?: React.ReactNode;
}> = ({ label, isRequired = false, children }) => {
  return (
    <div className="form-item">
      <div className="form-item-label">
        {isRequired ? <span style={{ color: 'red' }}>* </span> : null}
        {label}：
      </div>
      <div className="form-item-content">{children}</div>
    </div>
  );
};

interface Props {
  currentNum: number;
  env: 'prod' | 'pre';
  isPreview?: boolean;
  schemaOptions?: SchemaOptions;
  showDownBtn?: boolean;
  showRemoveBtn?: boolean;
  showUpBtn?: boolean;
  value: Value;

  onChange?: (value: Value) => void;
  onItemUp?: () => void;
  onItemDown?: () => void;
  onItemRemove?: () => void;
}

const BoardItem = ({
  currentNum,
  env,
  isPreview = false,
  schemaOptions,
  showDownBtn = false,
  showRemoveBtn = false,
  showUpBtn = false,
  value,

  onChange,
  onItemUp = () => {},
  onItemDown = () => {},
  onItemRemove = () => {},
}: Props) => {
  const imgOptions = schemaOptions?.imgOptions || {};
  const validateInfo = parseUiValidate(imgOptions);
  const [imageData, setImageData] = useState(
    value.cornerIconUrl
      ? [
          {
            url: value.cornerIconUrl,
            name: value.cornerIconUrl,
            downloadUrl: value.cornerIconUrl,
          },
        ]
      : undefined
  );
  const activityTypeDataSource = useMemo(() => {
    if (schemaOptions?.activityType?.length > 0) {
      return defaultActivityType.filter((item) => {
        return schemaOptions.activityType?.find(
          (type) => +type === +item.value
        );
      });
    } else {
      // 返回 defaultActivityType 只包含第一个的数组
      return [defaultActivityType[0]];
    }
  }, [schemaOptions]);

  // 活动id
  // const [activityDataSource, setActivityDataSource] = useState([]);

  // TODO：支持根据「活动类型」sug「活动id」
  // const onSearch = useRef(
  //   _.debounce(
  //     (keyword: string, boardCreateType: string, boardType: string) => {
  //       if (boardCreateType === 'algo') {
  //         return;
  //       }

  //       if (!keyword) {
  //         setActivityDataSource([]);
  //         return;
  //       }

  //       request<Response<{ id: number; name: string }[]>>({
  //         env,
  //         method: 'POST',
  //         api: '/api/boardManage/queryBoard',
  //         data: {
  //           boardCreateType,
  //           boardType,
  //           keyword,
  //         },
  //       }).then((res) => {
  //         if (res.success && res?.data?.length > 0) {
  //           setActivityDataSource(
  //             res.data.map((item) => {
  //               return {
  //                 ...item,
  //                 value: item.id,
  //                 label: `${item.id}-${item.name}`,
  //               };
  //             })
  //           );
  //         } else {
  //           Message.error(res.msg || '获取活动失败，请重试');
  //           setActivityDataSource([]);
  //         }
  //       });
  //     },
  //     500
  //   )
  // );

  const onUpdate = async (option: any) => {
    let aborted = false;
    const reader = new FileReader();
    reader.onload = () => {
      const base64 = reader?.result as string;

      const _base64 = base64?.replace(/^data\:.*?;base64,/, '');
      request<
        Response<string> & { code: string; success: boolean; msg: string }
      >({
        env,
        method: 'POST',
        api: '/api/pic/uploadPic',
        data: {
          userId: '1',
          base64: _base64,
          name: option?.file?.name,
        },
      })
        .then((res) => {
          if (res.success && res.code === '200') {
            if (!aborted) {
              option.onSuccess({ success: true, url: res.data });
            }
            Message.show('上传成功');
          } else {
            Message.error(`上传失败，${res.msg || '未知错误'}`);
          }
        })
        .catch((e) => {
          Message.error(
            `上传失败，${
              e.error && (e?.error?.message || e?.message || '未知错误')
            }`
          );
          if (!aborted) {
            option.onError(e);
          }
        });
    };
    reader.readAsDataURL(option.file);
    return {
      abort() {
        aborted = true;
      },
    };
  };

  return (
    <div className="nr-pit-select-item">
      <div className="nbsi-title">
        <div>
          <div className="nbsi-title-num">{currentNum}</div>
        </div>

        <div className="nbsi-title-btns">
          {showUpBtn ? (
            <Button className="nbsi-btn" onClick={onItemUp} text type="primary">
              上移
            </Button>
          ) : null}

          {showDownBtn ? (
            <Button
              className="nbsi-btn"
              onClick={onItemDown}
              text
              type="primary"
            >
              下移
            </Button>
          ) : null}
          {showRemoveBtn ? (
            <Button onClick={onItemRemove} text type="primary">
              删除
            </Button>
          ) : null}
        </div>
      </div>
      <FormItem label="召回个数" isRequired>
        <NumberPicker
          style={{ width: 120 }}
          isPreview={isPreview}
          value={value?.recallSize}
          min={0}
          max={999999}
          onChange={(v) => onChange({ ...value, recallSize: v })}
        />
      </FormItem>
      <FormItem label="投放活动类型" isRequired>
        <Radio.Group
          disabled={isPreview}
          value={value.activityType}
          onChange={(v: ActivityType) => {
            // setActivityDataSource([]);
            onChange({ ...value, activityType: v, activityId: undefined });
          }}
        >
          {activityTypeDataSource?.map((item) => {
            return (
              <Radio key={item.value} value={item.value}>
                {item.label}
              </Radio>
            );
          })}
        </Radio.Group>
      </FormItem>
      <FormItem label="投放活动ID" isRequired>
        <NumberPicker
          style={{ minWidth: 120 }}
          isPreview={isPreview}
          value={value?.activityId}
          onChange={(v) => onChange({ ...value, activityId: v })}
        />
      </FormItem>
      {schemaOptions?.showCornerIcon ? (
        <FormItem label="角标">
          <div className={styles.pit_img}>
            <Upload.Card
              className="alsc-form-item-uploader"
              accept={defaultAccept}
              disabled={isPreview}
              limit={1}
              request={onUpdate}
              // @ts-ignore
              beforeUpload={(file) => beforeUpload(file, validateInfo)}
              value={imageData}
              onChange={(v: any) => {
                setImageData(v);
                onChange({ ...value, cornerIconUrl: v?.[0]?.url });
              }}
            />
            {validateInfo && (
              <div className={styles.restriction_text}>
                <div className={styles.restrict_item}>
                  上传尺寸：
                  {[
                    validateInfo.width && `宽${validateInfo.width}px`,
                    validateInfo.minWidth && `宽>${validateInfo.minWidth}px`,
                    validateInfo.maxWidth && `宽<${validateInfo.maxWidth}px`,
                    validateInfo.height && `高${validateInfo.height}px`,
                    validateInfo.minHeight && `高>${validateInfo.minHeight}px`,
                    validateInfo.maxHeight && `高<${validateInfo.maxHeight}px`,
                  ]
                    ?.filter((x) => !!x)
                    ?.join(', ')}
                </div>
                <div className={styles.restrict_item}>
                  上传格式：{validateInfo.accept}
                </div>
                {(validateInfo.minSize || validateInfo.maxSize) && (
                  <div className={styles.restrict_item}>
                    图片大小：
                    {[
                      validateInfo.minSize && `体积>${validateInfo.minSize}kb`,
                      validateInfo.maxSize && `体积<${validateInfo.maxSize}kb`,
                    ]
                      .filter((x) => !!x)
                      .join(', ')}
                  </div>
                )}
              </div>
            )}
          </div>
        </FormItem>
      ) : null}
    </div>
  );
};
export default BoardItem;
