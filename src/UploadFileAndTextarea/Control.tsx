import "./Control.scss";

import { useMemo, useState } from "react";
import { Input, Message, Radio } from "@alifd/next";
import { UploadFile } from "@ali/nr-resource-components";

import { Props, SchemaOptions, SelectType } from "./types";

// 上传文件
export default function Control({
  env,
  schema,
  value: defaultValue,
  onChange,
}: Props) {
  const schemaOptions = schema?.options || ({} as SchemaOptions);
  // 接口返回的 value 是 string 类型
  const value = useMemo(() => {
    if (defaultValue && typeof defaultValue === "string") {
      return JSON.parse(defaultValue);
    } else {
      return defaultValue;
    }
  }, [defaultValue]);
  const [type, setType] = useState<SelectType>(value?.type || SelectType.text);

  const onTextChange = (v: string) => {
    if (!v) {
      onChange(undefined);
    } else {
      let _value = v;
      if (schemaOptions.maxCount) {
        // 将字符串中所有中文逗号转为英文逗号
        let valueList = v
          ?.replace(/，/g, ",")
          ?.split(/[,]/)
          ?.map((item) => item.trim());
        if (valueList.length > schemaOptions.maxCount) {
          valueList = valueList.slice(0, schemaOptions.maxCount);
          _value = valueList.join(",");
          Message.warning(`最多支持输入 ${schemaOptions.maxCount} 个值`);
        } else {
          _value = valueList.join(",");
        }
      }
      onChange?.({
        type: SelectType.text,
        value: _value,
      });
    }
  };

  return (
    <div className="nr-upload-file-and-textarea">
      <Radio.Group
        className="select-type"
        value={type}
        onChange={(v) => {
          onChange(undefined);
          setType(v as SelectType);
        }}
      >
        <Radio value={SelectType.text}>直接输入</Radio>
        <Radio value={SelectType.file}>文件上传</Radio>
      </Radio.Group>

      {/* 文本输入 */}
      {type === SelectType.text ? (
        <Input.TextArea
          maxLength={schemaOptions.maxLength}
          minLength={schemaOptions.minLength || 0}
          placeholder={schemaOptions.placeholder || "请输入"}
          showLimitHint
          style={{ width: "auto" }}
          value={value?.type === SelectType.text ? value?.value : undefined}
          onChange={(e) => {
            onTextChange(e);
          }}
        />
      ) : null}

      {/* 文件上传 */}
      {type === SelectType.file ? (
        <UploadFile
          env={env}
          schema={schema}
          value={value?.type === SelectType.file ? value?.value : undefined}
          onChange={(v) => {
            if (!v) {
              onChange(undefined);
              setType(SelectType.file);
            } else {
              onChange?.({
                type: SelectType.file,
                value: JSON.stringify(v),
              });
            }
          }}
        />
      ) : null}
    </div>
  );
}
