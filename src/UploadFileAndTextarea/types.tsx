export enum SelectType {
  text = "text",
  file = "file",
}

export interface SchemaOptions {
  placeholder?: string;
  templateUrl: string;
  maxCount?: number;
  maxLength?: number;
  minLength?: number;
}

interface Value {
  type: SelectType;
  value: string; // 如果是 file 需要 JSON.stringify 转为 string 类型提交
}

export interface Props {
  env: "prod" | "pre";
  isPreview?: boolean;
  value?: Value;
  schema: {
    options: SchemaOptions;
  };

  onChange?: (value: Value | undefined) => void;
}
