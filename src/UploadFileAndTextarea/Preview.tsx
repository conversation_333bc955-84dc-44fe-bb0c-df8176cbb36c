import { useMemo } from "react";
import { UploadFile } from "@ali/nr-resource-components";

import { Props, SelectType } from "./types";

const selectTypeStr = {
  [SelectType.text]: "直接输入",
  [SelectType.file]: "文件上传",
};

// 上传文件和文本输入 详情，Value: 接口返回 string， 本地编辑存的是对象
export default function Preview({ env, schema, value: defaultValue }: Props) {
  const propsValue =
    defaultValue && typeof defaultValue === "string"
      ? JSON.parse(defaultValue)
      : defaultValue;

  const value = useMemo(() => {
    if (propsValue && propsValue?.type === SelectType.file) {
      return JSON.parse(propsValue.value);
    }
    return propsValue?.value;
  }, [propsValue]);

  if (!propsValue) {
    return null;
  }

  return (
    <div className="nr-upload-file-and-textarea">
      {/* 类型 */}
      <div className="select-type">
        <div>{selectTypeStr[propsValue?.type as SelectType]}</div>
      </div>

      {/* 文本输入 */}
      {propsValue.type === SelectType.text ? <div>{value}</div> : null}

      {/* 文件上传 */}
      {propsValue.type === SelectType.file ? (
        <UploadFile env={env} schema={schema} isPreview={true} value={value} />
      ) : null}
    </div>
  );
}
