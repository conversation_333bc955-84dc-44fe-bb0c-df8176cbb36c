import axios from "axios";

// 使用独立的 axios 对象实例，避免被其他 axios 对象以及全局 axios 对象影响
const axiosInst = axios.create();

export const apiHostMapping = {
  pre: "https://pre-api-newretail-delivery-platform.ele.alibaba-inc.com",
  prod: "https://api-newretail-delivery-platform.ele.alibaba-inc.com",
};

interface Request {
  baseURL?: { pre: string; prod: string };
  env: "pre" | "prod";
  method: "POST" | "GET";
  api: string;
  params?: any;
  data?: any;
}

export async function request<T>({
  env,
  baseURL,
  method,
  api,
  params,
  data,
}: Request): Promise<T | null | undefined> {
  const res = await axiosInst({
    withCredentials: true,
    method,
    baseURL: baseURL ? baseURL[env] : apiHostMapping[env],
    url: `${api}`,
    headers: {
      "X-Login-Auto-Redirection": "false",
    },
    params,
    data,
  });
  return res?.data;
}

// 这里的 Response 不是用来定义 request 函数返回数据的数据结构的，是对外提供的工具类型
export interface Response<T> {
  data: T;
}
