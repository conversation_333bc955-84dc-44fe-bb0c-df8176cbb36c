// CpvAttribute特有的工具函数
export const cpvUtils = {
  // 检查数组是否在指定范围内
  isArrayLengthValid: (arr: any[], min: number, max: number): boolean => {
    return arr.length >= min && arr.length <= max;
  },
  // 安全获取数组长度
  getArrayLength: (arr: any[] | undefined): number => {
    return arr ? arr.length : 0;
  },
  // 检查ID是否有效
  isValidId: (id: any): boolean => {
    return typeof id === 'number' && id > 0;
  }
};
