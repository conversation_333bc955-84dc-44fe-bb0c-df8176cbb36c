import React from "react";
import { CpvAttributeData, CpvAttributeProps } from "./types";

const Component: React.FC<CpvAttributeProps> = ({ value }) => {
  const _value: CpvAttributeData =
    typeof value === "string" ? JSON.parse(value) : value;
  return (
    <div className="cpv-attribute-component">
      <div className="attribute-type-section">
        cpv属性：{_value.tagKeyName}({_value.tagKeyId})
      </div>
      <div className="attribute-value-section">
        cpv属性枚举值：
        {_value.tagValueList.map((item, i) => {
          return `${item.tagValueName}(${item.tagValueId})${
            i < _value.tagValueList.length - 1 ? "，" : ""
          }`;
        })}
      </div>
    </div>
  );
};

export default Component;
