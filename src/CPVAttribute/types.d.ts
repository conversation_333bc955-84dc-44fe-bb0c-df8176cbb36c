// CPV属性数据源项
export interface DataSourceItem {
  value: number;
  label: string;
  children?: DataSourceItem[];
}

// CPV属性输出结构
export interface CpvAttributeData {
  tagKeyId: number; //cpv属性项id
  tagKeyName: string; //cpv属性项名称
  tagValueList: {
    tagValueId: number; //cpv属性值id
    tagValueName: string; //cpv属性值名称
  }[];
}

// 接口响应结构
export interface Response<T> {
  data?: T;
  code?: string;
  msg?: string;
}

// Schema配置选项
export interface SchemaOptions {
  minCpvValueCount?: number;
  maxCpvValueCount?: number;
}

// 请求参数接口
export interface RequestOptions {
  env: "prod" | "pre";
  method: "GET" | "POST" | "PUT" | "DELETE";
  api: string;
  params?: Record<string, any>;
}

// CpvAttribute组件Props类型
export interface CpvAttributeProps {
  env: "prod" | "pre";
  isPreview?: boolean;
  schema?: {
    options: SchemaOptions;
  };
  value?: CpvAttributeData;
  onChange?: (value: CpvAttributeData | undefined) => void;
}
