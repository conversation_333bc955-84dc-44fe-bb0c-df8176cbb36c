import React, { useState, useEffect, useMemo, useCallback } from "react";
import { Select, Message, Loading } from "@alifd/next";
import {
  CpvAttributeData,
  CpvAttributeProps,
  DataSourceItem,
  SchemaOptions,
  Response,
} from "./types.d";
import { request } from "pkg/request";
import "./styles.scss";
import { cpvUtils } from "./utils";

const { Option } = Select;

const CpvAttributeComponent: React.FC<CpvAttributeProps> = ({
  env,
  isPreview = false,
  schema,
  value,
  onChange,
}) => {
  // 从schema中获取配置选项
  const options = useMemo(
    (): Required<SchemaOptions> => ({
      minCpvValueCount: schema?.options?.minCpvValueCount || 1,
      maxCpvValueCount: schema?.options?.maxCpvValueCount || 10,
    }),
    [schema]
  );

  // 使用state存储数据
  const [selectedTagKey, setSelectedTagKey] = useState<
    { id: number; name: string } | undefined
  >(undefined);
  const [selectedTagValues, setSelectedTagValues] = useState<
    { id: number; name: string }[]
  >([]);

  // 接口数据状态
  const [dataSource, setDataSource] = useState<DataSourceItem[]>([]);
  const [attributeValues, setAttributeValues] = useState<DataSourceItem[]>([]);
  const [loadingTypes, setLoadingTypes] = useState<boolean>(false);

  // 获取CPV属性数据源
  useEffect(() => {
    setLoadingTypes(true);
    request<Response<DataSourceItem[]>>({
      env,
      method: "GET",
      api: "/api/common/listItmSaroCpvCfg",
    })
      .then((res) => {
        if (res.code === "200" && res.data) {
          setDataSource(res.data);
        } else {
          Message.error(res.msg || "获取属性数据失败");
        }
      })
      .catch((error) => {
        Message.error("获取属性数据失败");
        console.error("获取属性数据失败:", error);
      })
      .finally(() => {
        setLoadingTypes(false);
      });
  }, [env]);

  // 根据属性类型获取属性值列表
  useEffect(() => {
    if (selectedTagKey && dataSource.length > 0) {
      const selectedType = dataSource.find(
        (item) => item.value === selectedTagKey.id
      );
      if (selectedType && selectedType.children) {
        setAttributeValues(selectedType.children);
      } else {
        setAttributeValues([]);
      }
    } else {
      setAttributeValues([]);
    }
  }, [selectedTagKey, dataSource]);

  // 初始化数据
  useEffect(() => {
    if (value) {
      const _value: CpvAttributeData = typeof value === 'string' ? JSON.parse(value) : value;
      setSelectedTagKey({
        id: _value.tagKeyId,
        name: _value.tagKeyName
      });
      // 转换tagValueList格式
      const convertedTagValues = (_value.tagValueList || []).map(item => ({
        id: item.tagValueId,
        name: item.tagValueName
      }));
      setSelectedTagValues(convertedTagValues);
    }
  }, []);

  // 检查是否满足onChange条件并触发
  const checkAndTriggerChange = useCallback((currentSelectedTagKey,selectedTagVals: any[]) => {
    if (!onChange) return;

    // 检查赋值条件
    if (
      currentSelectedTagKey &&
      selectedTagVals &&
      cpvUtils.getArrayLength(selectedTagVals) >= options.minCpvValueCount
    ) {
      const updatedValue: CpvAttributeData = {
        tagKeyId: currentSelectedTagKey.id,
        tagKeyName: currentSelectedTagKey.name,
        tagValueList: selectedTagVals?.map((item) => ({
          tagValueId: item.id,
          tagValueName: item.name,
        })),
      };

      // 只有当值真正发生变化时才触发onChange
      const currentValueStr = JSON.stringify(value);
      const updatedValueStr = JSON.stringify(updatedValue);
      if (currentValueStr !== updatedValueStr) {
        console.log('--updatedValue', updatedValue);
        onChange(updatedValue);
      }
      return;
    }

    // 检查清空条件
    if (
      !currentSelectedTagKey ||
      !selectedTagVals ||
      cpvUtils.getArrayLength(selectedTagVals) < options.minCpvValueCount
    ) {
      if (value !== undefined) {
        onChange(undefined);
      }
    }
  }, [
    selectedTagKey,
    selectedTagValues,
    value,
    onChange,
    options.minCpvValueCount,
  ]);

  // 切换CPV属性类型时清空属性值
  const handleCpvAttributeTypeChange = (attributeTypeId: number): void => {
    const selectedType = dataSource.find(
      (item) => item.value === attributeTypeId
    );
    if (selectedType) {
      setSelectedTagKey({
        id: selectedType.value,
        name: selectedType.label,
      });
      setSelectedTagValues([]);

      // 直接调用检查函数
      checkAndTriggerChange({
        id: selectedType.value,
        name: selectedType.label,
      },[]);
    }
  };

  // 更新CPV属性值
  const handleCpvAttributeValuesChange = (
    attributeValueIds: number[]
  ): void => {
    // 检查是否超过最大选择数量
    if (
      !cpvUtils.isArrayLengthValid(
        attributeValueIds,
        0,
        options.maxCpvValueCount
      )
    ) {
      Message.warning(`最多只能选择${options.maxCpvValueCount}个属性值`);
      return;
    }

    // 检查是否少于最小选择数量
    if (
      cpvUtils.getArrayLength(attributeValueIds) > 0 &&
      cpvUtils.getArrayLength(attributeValueIds) < options.minCpvValueCount
    ) {
      Message.warning(`至少需要选择${options.minCpvValueCount}个属性值`);
    }

    // 根据选中的ID找到对应的名称
    const selectedValues = attributeValueIds
      .map((id) => {
        const valueItem = attributeValues.find((item) => item.value === id);
        return {
          id: id,
          name: valueItem ? valueItem.label : "",
        };
      })
      .filter((item) => item.name); // 过滤掉找不到名称的项

    setSelectedTagValues(selectedValues);

    // 直接调用检查函数
    checkAndTriggerChange(selectedTagKey, selectedValues);
  };

  return (
    <div className="cpv-attribute-component">
      <div className="attribute-type-section">
        <Loading visible={loadingTypes} style={{ width: "200px" }}>
          <Select
            value={selectedTagKey?.id}
            onChange={handleCpvAttributeTypeChange}
            placeholder="请选择属性类型"
            className="attribute-type-select"
            showSearch
            filterLocal
            onSearch={() => {}}
            disabled={loadingTypes}
          >
            {dataSource.map((item) => (
              <Option key={item.value} value={item.value}>
                {item.label}
              </Option>
            ))}
          </Select>
        </Loading>
      </div>

      {selectedTagKey && attributeValues.length > 0 && (
        <div className="attribute-values-section">
          <span className="label">cpv属性枚举值：</span>
          <Select
            mode="multiple"
            value={selectedTagValues.map((item) => item.id)}
            onChange={handleCpvAttributeValuesChange}
            placeholder={`请选择属性值（至少${options.minCpvValueCount}个，最多${options.maxCpvValueCount}个）`}
            className="attribute-values-select"
            showSearch
            filterLocal
            onSearch={() => {}}
            maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}...`}
            disabled={attributeValues.length === 0}
          >
            {attributeValues.map((valueItem) => (
              <Option key={valueItem.value} value={valueItem.value}>
                {valueItem.label}
              </Option>
            ))}
          </Select>
        </div>
      )}
    </div>
  );
};

export default CpvAttributeComponent;
