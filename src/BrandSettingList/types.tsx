export interface Value {
  brandName: string;
  content: string;
  imageUrl: string;
  key: number;
  rightId: number;
}

export interface ImgOptions {
  limit?: number;
  minSize?: number;
  maxSize?: number;
  accept?: string;
  maxWidth?: number;
  minWidth?: number;
  height?: number;
  maxHeight?: number;
  minHeight?: number;
  placeholder?: string;
  width?: number;
}

export interface SchemaOptions {
  limit?: number; // 列表最大数量
  imgOptions?: ImgOptions; // 图片宽高大小等配置
}

export interface Schema {
  options: SchemaOptions;
}

export interface Props {
  env: 'prod' | 'pre';
  isPreview?: boolean;
  schema: Schema;
  value?: Value[];

  onChange?: (value: Value[]) => void;
}
