import { useEffect, useState } from 'react';

import "./styles.scss";
import { Props, Value } from './types';
import { genId } from './utils';
import BrandItem from './components/BrandItem';
import { Button } from '@alifd/next';

// 门店类目 创建/编辑，Value: string[];
export default function Control({ env, schema, value, onChange }: Props) {
  const schemaOptions = schema?.options || {};
  const [brandList, setBrandList] = useState<Value[]>(
    value?.length > 0
      ? value.map((item) => ({ ...item, key: genId() }))
      : [{ key: genId() } as Value]
  );

  useEffect(() => {
    // 只同步有效数据 —— 必填项都需要校验
    const validList = brandList.filter((item) => {
      return item.content && item.imageUrl && item.key;
    });

    // undefined 用于表单校验
    onChange(validList?.length > 0 ? validList : undefined);
  }, [brandList]);

  return (
    <div className="nr-brand-setting-list">
      {brandList?.map((item, index) => {
        return (
          <BrandItem
            key={item.key}
            currentNum={index + 1}
            env={env}
            schemaOptions={schemaOptions}
            showRemoveBtn={true}
            value={item}
            onChange={(v: Value) => {
              const curIndex = brandList.findIndex((i) => i.key === v.key);
              if (curIndex === -1) {
                setBrandList([...brandList, v]);
              } else {
                setBrandList([
                  ...brandList.slice(0, curIndex),
                  v,
                  ...brandList.slice(curIndex + 1),
                ]);
              }
            }}
            onItemRemove={() => {
              const _value = [...brandList];
              const curIndex = _value.findIndex((i) => i.key === item.key);
              if (curIndex > -1) {
                _value.splice(curIndex, 1);
                setBrandList(_value);
              }
            }}
          />
        );
      })}

      {/* 新增 */}
      {brandList?.length < (schemaOptions?.limit || 1) ? (
        <Button
          onClick={() => {
            setBrandList([...brandList, { key: genId() } as Value]);
          }}
          text
          type="primary"
        >
          新增
        </Button>
      ) : null}
    </div>
  );
}
