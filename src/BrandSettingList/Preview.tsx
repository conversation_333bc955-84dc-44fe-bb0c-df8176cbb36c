import { useMemo } from 'react';

import BrandItem from './components/BrandItem';
import { Props, Value } from './types';
import { genId } from './utils';

// 门店类目 详情，Value: string[];
export default function Preview({ env, schema, value }: Props) {
  const brandList: Value[] = useMemo(() => {
    return value?.length > 0
      ? value.map((item) => ({ ...item, key: genId() }))
      : [];
  }, [value]);

  return (
    <div className="nr-brand-setting-list">
      {brandList?.map((item, i) => {
        return (
          <BrandItem
            key={item.key}
            currentNum={i + 1}
            env={env}
            value={item}
            schemaOptions={schema?.options}
            isPreview={true}
          />
        );
      })}
    </div>
  );
}
