import React, { useState } from 'react';
import { Button, Input, Message, NumberPicker, Upload } from '@alifd/next';
import { request, Response } from 'pkg/request';

import './styles.scss';

import { SchemaOptions, Value } from '../types';
import { beforeUpload, parseUiValidate } from '../utils';

const defaultAccept =
  'image/png, image/jpg, image/jpeg, image/apng, image/gif, image/bmp';

const FormItem: React.FC<{
  label: string;
  isRequired?: boolean;
  children?: React.ReactNode;
}> = ({ label, isRequired = false, children }) => {
  return (
    <div className="nbi-form-item">
      <div className="nbi-form-item-label">
        {isRequired ? <span style={{ color: 'red' }}>* </span> : null}
        {label}：
      </div>
      <div className="nvi-form-item-content">{children}</div>
    </div>
  );
};

interface Props {
  currentNum: number;
  env: 'prod' | 'pre';
  isPreview?: boolean;
  schemaOptions?: SchemaOptions;
  showDownBtn?: boolean;
  showRemoveBtn?: boolean;
  showUpBtn?: boolean;
  value: Value;

  onChange?: (value: Value) => void;
  onItemUp?: () => void;
  onItemDown?: () => void;
  onItemRemove?: () => void;
}

const BrandItem = ({
  currentNum,
  env,
  isPreview = false,
  schemaOptions,
  showDownBtn = false,
  showRemoveBtn = false,
  showUpBtn = false,
  value,

  onChange,
  onItemUp = () => {},
  onItemDown = () => {},
  onItemRemove = () => {},
}: Props) => {
  const imgOptions = schemaOptions?.imgOptions || {};
  const validateInfo = parseUiValidate(imgOptions);
  const [imageData, setImageData] = useState(
    value.imageUrl
      ? [
          {
            url: value.imageUrl,
            name: value.imageUrl,
            downloadUrl: value.imageUrl,
          },
        ]
      : undefined
  );

  const onUpdate = async (option: any) => {
    let aborted = false;
    const reader = new FileReader();
    reader.onload = () => {
      const base64 = reader?.result as string;

      const _base64 = base64?.replace(/^data\:.*?;base64,/, '');
      request<
        Response<string> & { code: string; success: boolean; msg: string }
      >({
        env,
        method: 'POST',
        api: '/api/pic/uploadPic',
        data: {
          userId: '1',
          base64: _base64,
          name: option?.file?.name,
        },
      })
        .then((res) => {
          if (res.success && res.code === '200') {
            if (!aborted) {
              option.onSuccess({ success: true, url: res.data });
            }
            Message.show('上传成功');
          } else {
            Message.error(`上传失败，${res.msg || '未知错误'}`);
          }
        })
        .catch((e) => {
          Message.error(
            `上传失败，${
              e.error && (e?.error?.message || e?.message || '未知错误')
            }`
          );
          if (!aborted) {
            option.onError(e);
          }
        });
    };
    reader.readAsDataURL(option.file);
    return {
      abort() {
        aborted = true;
      },
    };
  };

  return (
    <div className="nr-brand-item">
      <div className="nbi-title">
        <div>
          <div className="nbi-title-num">{currentNum}</div>
        </div>

        <div className="nbi-title-btns">
          {showUpBtn ? (
            <Button className="nbi-btn" onClick={onItemUp} text type="primary">
              上移
            </Button>
          ) : null}

          {showDownBtn ? (
            <Button
              className="nbi-btn"
              onClick={onItemDown}
              text
              type="primary"
            >
              下移
            </Button>
          ) : null}
          {showRemoveBtn ? (
            <Button onClick={onItemRemove} text type="primary">
              删除
            </Button>
          ) : null}
        </div>
      </div>
      <FormItem label="品牌名称">
        <Input
          isPreview={isPreview}
          value={value?.brandName}
          placeholder="最多输入10个字"
          maxLength={10}
          onChange={(v) => onChange({ ...value, brandName: v })}
        />
      </FormItem>
      <FormItem label="品牌祝福语" isRequired>
        <Input
          isPreview={isPreview}
          value={value?.content}
          placeholder="最多输入32个字"
          maxLength={32}
          onChange={(v) => onChange({ ...value, content: v })}
        />
      </FormItem>
      <FormItem label="品牌贺卡图片" isRequired>
        <div className="nbi-img-upload-wrap">
          <Upload.Card
            accept={defaultAccept}
            disabled={isPreview}
            limit={1}
            request={onUpdate}
            // @ts-ignore
            beforeUpload={(file) => beforeUpload(file, validateInfo)}
            value={imageData}
            onChange={(v: any) => {
              setImageData(v);
              onChange({ ...value, imageUrl: v?.[0]?.url });
            }}
          />
          {validateInfo && (
            <div className="nbi-restriction-text">
              <div className="nbi-restrict-item">
                上传尺寸：
                {[
                  validateInfo.width && `宽${validateInfo.width}px`,
                  validateInfo.minWidth && `宽>${validateInfo.minWidth}px`,
                  validateInfo.maxWidth && `宽<${validateInfo.maxWidth}px`,
                  validateInfo.height && `高${validateInfo.height}px`,
                  validateInfo.minHeight && `高>${validateInfo.minHeight}px`,
                  validateInfo.maxHeight && `高<${validateInfo.maxHeight}px`,
                ]
                  ?.filter((x) => !!x)
                  ?.join(', ')}
              </div>
              <div className="nbi-restrict-item">
                上传格式：{validateInfo.accept}
              </div>
              {(validateInfo.minSize || validateInfo.maxSize) && (
                <div className="nbi-restrict-item">
                  图片大小：
                  {[
                    validateInfo.minSize && `体积>${validateInfo.minSize}kb`,
                    validateInfo.maxSize && `体积<${validateInfo.maxSize}kb`,
                  ]
                    .filter((x) => !!x)
                    .join(', ')}
                </div>
              )}
            </div>
          )}
        </div>
      </FormItem>
      <FormItem label="品牌权益投放计划ID">
        <NumberPicker
          style={{ minWidth: 120 }}
          isPreview={isPreview}
          value={value?.rightId}
          onChange={(v) => {
            let _v = +v;
            if (isNaN(_v)) {
              _v = undefined;
            }
            onChange({ ...value, rightId: _v });
          }}
        />
      </FormItem>
    </div>
  );
};
export default BrandItem;
