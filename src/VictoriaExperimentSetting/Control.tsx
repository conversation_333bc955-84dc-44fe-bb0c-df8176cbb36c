import "./Control.scss";

import { useDebounceEffect } from "ahooks";
import { useEffect, useMemo, useState } from "react";
import { Button, Checkbox, Loading, Select } from "@alifd/next";

import { request, Response } from "pkg/request";
import { Experiment, Factor, Props } from "./types";

export default function Control({ env, value, onChange }: Props) {
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState<string>();
  const [dataSource, setDataSource] = useState([]);
  const [factorStateCache, setFactorStateCache] = useState<
    Record<string, Factor[]>
  >({});
  const [fallbackOptionLabel, setFallbackOptionLabel] = useState<string>();

  useDebounceEffect(() => {
    if (!searchValue) {
      return;
    }
    request<Response<{ rows?: Experiment[] }>>({
      env,
      method: "POST",
      api: "/api/deliverymanage/queryExperiment",
      data: {
        page: 1,
        size: 20,
        query: {
          name: searchValue,
        },
      },
    }).then((res) => {
      const result = res?.data?.rows || [];
      setDataSource(
        result.map((item) => ({
          label: item.name,
          value: item.id,
        }))
      );
    });
  }, [searchValue, env]);

  useEffect(() => {
    if (!value?.experimentId) {
      return () => {};
    }
    const item = dataSource?.find((_item) => _item.value === value?.experimentId);
    if (item) {
      return () => {};
    }

    let destroy = false;
    request<Response<{ rows?: Experiment[] }>>({
      env,
      method: "POST",
      api: "/api/deliverymanage/queryExperiment",
      data: {
        page: 1,
        size: 20,
        query: {
          name: value.experimentId,
        },
      },
    }).then((res) => {
      if (!destroy) {
        res?.data?.rows?.forEach((rowItem) => {
          if (rowItem.id === value?.experimentId) {
            setFallbackOptionLabel(rowItem.name);
          }
        });
      }
    });

    return () => {
      destroy = true;
    };
  }, [value, dataSource]);

  useEffect(() => {
    const id = value?.experimentId;
    if (!id) {
      return;
    }
    if (factorStateCache[id]?.length) {
      return;
    }
    setLoading(true);
    request<Response<{ factors: Factor[] }>>({
      env,
      method: "POST",
      api: "/api/deliverymanage/queryExperimentFactor",
      data: { id },
    })
      .then((res) => {
        setFactorStateCache((prev) => {
          return {
            ...prev,
            [id]: res?.data?.factors,
          };
        });
      })
      .finally(() => {
        setLoading(false);
      });
  }, [value?.experimentId]);

  const factors = useMemo<Factor[] | undefined>(() => {
    return factorStateCache[value?.experimentId];
  }, [value?.experimentId, factorStateCache]);

  return (
    <div className="nr-victoria-exp-control">
      <div style={{ display: "flex", alignItems: "center" }}>
        <Select
          style={{ marginRight: 8, width: 300 }}
          value={value?.experimentId}
          hasClear
          dataSource={dataSource}
          showSearch
          filterLocal={false}
          valueRender={(item) => {
            const label = dataSource?.find(
              (i) => i.value === item.value
            )?.label;
            if (label) {
              return label;
            }
            if (fallbackOptionLabel) {
              return fallbackOptionLabel;
            }
            return item.value;
          }}
          onSearch={(v) => setSearchValue(v)}
          placeholder="请输入实验ID"
          onChange={async (v) => {
            if (!v) {
              onChange?.(undefined);
              return;
            }
            onChange?.({
              experimentId: v,
              factors: [],
            });
          }}
        />
        <Button
          type="primary"
          text
          onClick={() => {
            window.open(
              "https://ab.elenet.me/test-create-new/index#sceneId=69&kind=1",
              "_blank"
            );
          }}
        >
          去创建
        </Button>
      </div>
      <Loading
        visible={loading}
        size="medium"
        style={{ display: "block", maxWidth: 500, minHeight: loading ? 60 : 0 }}
      >
        {value?.experimentId && factors?.length ? (
          <div style={{ marginTop: 12 }}>
            {factors.map((factor) => {
              const index = value.factors.findIndex(
                (f) => f.key === factor.factorKey
              );
              const checked = index !== -1;
              const f = index === -1 ? undefined : value.factors[index];
              return (
                <div key={factor.factorKey} className="nr-param-row">
                  <div className="nr-flex-row" style={{ marginRight: 15 }}>
                    <Checkbox
                      checked={checked}
                      onChange={() => {
                        const _factors = value.factors.slice();
                        if (checked) {
                          _factors.splice(index, 1);
                        } else {
                          _factors.push({
                            key: factor.factorKey,
                            value: factor.factorValue?.[0],
                          });
                        }
                        onChange({ ...value, factors: _factors });
                      }}
                    />
                    &nbsp;
                    <label>分流参数：</label>
                    <span>{factor.factorKey}</span>
                  </div>
                  <div className="nr-flex-row">
                    <label>分流数值：</label>
                    <Select
                      value={f?.value}
                      placeholder="请选择"
                      style={{ width: 160 }}
                      disabled={!checked}
                      dataSource={factor.factorValue.map((factorValue) => {
                        return { label: factorValue, value: factorValue };
                      })}
                      onChange={(v) => {
                        const valueIndex = value.factors.findIndex(
                          (item) => item.key === factor.factorKey
                        );
                        const _factors = value.factors.slice();
                        if (valueIndex !== -1) {
                          _factors[valueIndex] = {
                            ..._factors[valueIndex],
                            value: v,
                          };
                        } else {
                          _factors.push({
                            key: factor.factorKey,
                            value: v,
                          });
                        }
                        onChange({ ...value, factors: _factors });
                      }}
                    />
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <></>
        )}
      </Loading>
    </div>
  );
}
