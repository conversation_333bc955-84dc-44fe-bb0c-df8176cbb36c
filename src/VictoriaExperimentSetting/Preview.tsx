import { useEffect, useMemo, useState } from "react";

import { request, Response } from "pkg/request";
import { Experiment, Props, Value } from "./types";

export default function Preview({ value, env }: Props) {
  const [exp, setExp] = useState<Experiment>();

  // TODO: 这个转换逻辑应该去掉，需要后端直接返回对象
  const finalValue = useMemo<Value>(() => {
    try {
      if (typeof value === "string") {
        return JSON.parse(value);
      } else {
        return value;
      }
    } catch (e) {
      console.error(e);
      return { expriemenId: undefined, factors: [] };
    }
  }, []);

  useEffect(() => {
    const id = finalValue.experimentId;
    if (!id) {
      return;
    }
    request<Response<{ rows?: Experiment[] }>>({
      env,
      method: "POST",
      api: "/api/deliverymanage/queryExperiment",
      data: {
        page: 1,
        size: 20,
        query: {
          name: id,
        },
      },
    }).then((r) => {
      setExp(r.data.rows?.[0]);
    });
  }, [finalValue, env]);

  return (
    <div>
      {exp?.name ? (
        <div>
          {exp?.name}(id: {finalValue?.experimentId || ""})
        </div>
      ) : (
        <div>{finalValue?.experimentId || ""}</div>
      )}
      {finalValue?.factors?.map((f) => {
        return (
          <div
            key={f.key}
            style={{ display: "flex", alignItems: "center", marginTop: 8 }}
          >
            <span style={{ color: "rgba(0, 0, 0, .64)" }}>分流参数：</span>
            <span>{f.key}</span>
            <span style={{ marginLeft: 8, color: "rgba(0, 0, 0, .64)" }}>
              分流数值：
            </span>
            <span>{f.value}</span>
          </div>
        );
      })}
    </div>
  );
}
