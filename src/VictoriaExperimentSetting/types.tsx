export interface Experiment {
  id: string | number;
  name: string;
}

export interface Factor {
  factorKey: string;
  factorValue: string[];
}

export interface Value {
  experimentId: string | number;
  factors: Partial<{ key: string; value: string | string }>[];
}

export interface Props {
  env: "prod" | "pre";
  isPreview?: boolean;
  value?: Value;
  onChange?: (value: Value | undefined) => void;
}
