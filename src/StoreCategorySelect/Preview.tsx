import { useEffect, useState } from 'react';

import { request, Response } from 'pkg/request';
import { Item, Props } from './types';

// 门店类目 详情，Value: string[];
export default function Preview({ env, value }: Props) {
  const [categoryStr, setCategoryStr] = useState('');

  useEffect(() => {
    request<Response<Item[]>>({
      env,
      method: 'GET',
      api: '/api/common/eleCategory',
    }).then((res) => {
      const dataSourceStrList: string[] = [];

      // 递归遍历 dataSource 根据 value 提取 DataSource 中对应的 label 值
      function getDataSourceStrList(dataSource: Item[], _value: string[]) {
        dataSource.map((item) => {
          if (_value.find((val) => val === item.value)) {
            dataSourceStrList.push(item.label);
            return item;
          } else if (item.children) {
            return getDataSourceStrList(item.children, _value);
          }
          return undefined;
        });
        return dataSourceStrList;
      }

      // 调用递归函数，获取 dataSourceStrList
      getDataSourceStrList(res?.data || [], value);

      // 处理 dataSourceStrList 为字符串
      setCategoryStr(dataSourceStrList.join('，'));
    });
  }, [env]);

  return <div>{categoryStr}</div>;
}
