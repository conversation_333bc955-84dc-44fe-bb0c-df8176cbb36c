import './Control.scss';

import { useEffect, useState } from 'react';
import { CascaderSelect } from '@alifd/next';

import { request, Response } from 'pkg/request';
import { Item, Props } from './types';

// 门店类目 创建/编辑，Value: string[];
export default function Control({ env, value, onChange }: Props) {
  const [dataSource, setDataSource] = useState<Item[]>([]);

  useEffect(() => {
    request<Response<Item[]>>({
      env,
      method: 'GET',
      api: '/api/common/eleCategory',
    }).then((res) => {
      setDataSource(res.data);
    });
  }, [env]);

  /**
   * @function _onChange 选择回调
   * @param value 值
   * @param data 值对应的对象
   */
  const _onChange = (_value: string[]) => {
    onChange(_value);
  };

  return (
    <div className="nr-stores-category-select">
      <CascaderSelect
        listStyle={{ minWidth: 200 }}
        style={{ width: '100%' }}
        expandTriggerType={'hover'}
        autoFocus
        value={value}
        dataSource={dataSource}
        multiple={true}
        onChange={_onChange}
      />
    </div>
  );
}
