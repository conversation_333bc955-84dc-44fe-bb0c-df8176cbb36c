import './styles.scss';

import { useEffect, useState } from 'react';
import { Button } from '@alifd/next';

import { Props, Value } from 'BoardSelect/types';
import BoardItem from './components/BoardItem';
import { genId } from './utils';

// 门店类目 创建/编辑，Value: string[];
export default function Control({ env, schema, value, onChange }: Props) {
  const schemaOptions = schema?.options || {};
  const [boardList, setBoardList] = useState<Value[]>(
    value?.length > 0
      ? value.map((item) => ({ ...item, key: genId() }))
      : [{ key: genId() } as Value]
  );

  useEffect(() => {
    // 只同步有效数据 —— 有 boardId 的才被认为有效数据
    const validList = boardList.filter((item) => {
      return item.boardId && item.key;
    });

    // undefined 用于表单校验
    onChange(validList?.length > 0 ? validList : undefined);
  }, [boardList]);

  return (
    <div className="nr-board-select">
      {boardList?.map((item, index) => (
        <BoardItem
          key={item.key}
          currentNum={index + 1}
          env={env}
          showDownBtn={index < boardList.length - 1}
          showRemoveBtn={true}
          showUpBtn={index > 0}
          value={item}
          onChange={(v: Value) => {
            const curIndex = boardList.findIndex((i) => i.key === v.key);
            if (curIndex === -1) {
              setBoardList([...boardList, v]);
            } else {
              setBoardList([
                ...boardList.slice(0, curIndex),
                v,
                ...boardList.slice(curIndex + 1),
              ]);
            }
          }}
          onItemDown={() => {
            const curIndex = boardList.findIndex((i) => i.key === item.key);
            if (curIndex < boardList.length - 1) {
              const newList = [
                ...boardList.slice(0, curIndex),
                boardList[curIndex + 1],
                boardList[curIndex],
                ...boardList.slice(curIndex + 2),
              ];
              setBoardList(newList);
            }
          }}
          onItemRemove={() => {
            const _value = [...boardList];
            const curIndex = _value.findIndex((i) => i.key === item.key);
            if (curIndex > -1) {
              _value.splice(curIndex, 1);
              setBoardList(_value);
            }
          }}
          onItemUp={() => {
            const curIndex = boardList.findIndex((i) => i.key === item.key);
            if (curIndex > 0) {
              const newList = [
                ...boardList.slice(0, curIndex - 1),
                boardList[curIndex],
                boardList[curIndex - 1],
                ...boardList.slice(curIndex + 1),
              ];
              setBoardList(newList);
            }
          }}
        />
      ))}

      {/* 新增 */}
      {boardList?.length < (schemaOptions?.limit || 1) ? (
        <Button
          onClick={() => {
            setBoardList([...boardList, { key: genId() } as Value]);
          }}
          text
          type="primary"
        >
          新增
        </Button>
      ) : null}
    </div>
  );
}
