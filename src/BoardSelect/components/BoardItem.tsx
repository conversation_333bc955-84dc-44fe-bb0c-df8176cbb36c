import React, { useEffect, useRef, useState } from 'react';
import {
  Button,
  CascaderSelect,
  Message,
  NumberPicker,
  Radio,
} from '@alifd/next';
import { BoardType, Item, Response, Value } from 'BoardSelect/types';
import { request } from 'pkg/request';

import _ from 'lodash';

const FormItem: React.FC<{ label: string; children?: React.ReactNode }> = ({
  label,
  children,
}) => {
  return (
    <div className="form-item">
      <div className="form-item-label">
        <span style={{ color: 'red' }}>* </span>
        {label}：
      </div>
      <div className="form-item-content">{children}</div>
    </div>
  );
};

interface Props {
  currentNum: number;
  env: 'prod' | 'pre';
  isPreview?: boolean;
  showDownBtn?: boolean;
  showRemoveBtn?: boolean;
  showUpBtn?: boolean;
  value: Value;

  onChange?: (value: Value) => void;
  onItemUp?: () => void;
  onItemDown?: () => void;
  onItemRemove?: () => void;
}

const BoardItem = ({
  currentNum,
  env,
  isPreview = false,
  showDownBtn = false,
  showRemoveBtn = false,
  showUpBtn = false,
  value,

  onChange,
  onItemUp = () => {},
  onItemDown = () => {},
  onItemRemove = () => {},
}: Props) => {
  // 榜单类型
  const [brandTypeDataSource, setBrandTypeDataSource] = useState<Item[]>([]);
  // 榜单
  const [brandDataSource, setBrandDataSource] = useState([]);

  useEffect(() => {
    // 获取榜单类型
    if (!value.boardCreateType) {
      return;
    }

    request<Response<Item[]>>({
      env,
      method: 'POST',
      api: '/api/boardManage/getBoardType',
      data: {
        boardCreateType: value.boardCreateType,
      },
    }).then((res) => {
      if (res.success) {
        setBrandTypeDataSource(res.data);
      } else {
        Message.error(res.msg || '获取榜单类型失败，请重试');
        setBrandTypeDataSource([]);
      }
    });
  }, [env, value.boardCreateType]);

  useEffect(() => {
    // 获取榜单列表
    if (!value.boardCreateType || !value.boardType) {
      return;
    }

    if (value.boardCreateType === 'algo') {
      // 算法榜单数量有限，可以直接请求
      request<Response<{ id: number; name: string }[]>>({
        env,
        method: 'POST',
        api: '/api/boardManage/queryBoard',
        data: {
          boardCreateType: value.boardCreateType,
          boardType: value.boardType,
          keyword: '',
        },
      }).then((res) => {
        if (res.success) {
          setBrandDataSource(
            res.data.map((item) => {
              return {
                ...item,
                value: item.id,
                label: `${item.id}-${item.name}`,
              };
            })
          );
        } else {
          Message.error(res.msg || '获取榜单失败，请重试');
          setBrandDataSource([]);
        }
      });
    } else if (value.boardCreateType === 'manual' && value.boardId) {
      // 编辑、详情回填人工数据
      request<Response<{ id: number; name: string }[]>>({
        env,
        method: 'POST',
        api: '/api/boardManage/queryBoard',
        data: {
          boardCreateType: value.boardCreateType,
          boardType: value.boardType,
          keyword: value.boardId,
        },
      }).then((res) => {
        if (res.success) {
          setBrandDataSource(
            res.data.map((item) => {
              return {
                ...item,
                value: item.id,
                label: `${item.id}-${item.name}`,
              };
            })
          );
        } else {
          Message.error(res.msg || '获取榜单失败，请重试');
          setBrandDataSource([]);
        }
      });
    }
  }, [env, value.boardCreateType, value.boardType]);

  // 使用 useRef 保证方法不会每次渲染都重新创建新方法
  // 人工榜单数量较大，需要通过 keyword 进行搜索，TODO：需要确认单次请求最大数量
  const onSearch = useRef(
    _.debounce(
      (keyword: string, boardCreateType: string, boardType: string) => {
        if (boardCreateType === 'algo') {
          return;
        }

        if (!keyword) {
          setBrandDataSource([]);
          return;
        }

        request<Response<{ id: number; name: string }[]>>({
          env,
          method: 'POST',
          api: '/api/boardManage/queryBoard',
          data: {
            boardCreateType,
            boardType,
            keyword,
          },
        }).then((res) => {
          if (res.success && res?.data?.length > 0) {
            setBrandDataSource(
              res.data.map((item) => {
                return {
                  ...item,
                  value: item.id,
                  label: `${item.id}-${item.name}`,
                };
              })
            );
          } else {
            Message.error(res.msg || '获取榜单失败，请重试');
            setBrandDataSource([]);
          }
        });
      },
      500
    )
  );

  return (
    <div className="nr-board-select-item">
      <div className="nbsi-title">
        <div>
          <div className="nbsi-title-num">{currentNum}</div>
        </div>

        <div className="nbsi-title-btns">
          {showUpBtn ? (
            <Button className="nbsi-btn" onClick={onItemUp} text type="primary">
              上移
            </Button>
          ) : null}

          {showDownBtn ? (
            <Button
              className="nbsi-btn"
              onClick={onItemDown}
              text
              type="primary"
            >
              下移
            </Button>
          ) : null}
          {showRemoveBtn ? (
            <Button onClick={onItemRemove} text type="primary">
              删除
            </Button>
          ) : null}
        </div>
      </div>
      <FormItem label="元数据类型">
        <Radio.Group
          disabled={isPreview}
          value={value.boardCreateType}
          onChange={(v: BoardType) => {
            setBrandTypeDataSource([]);
            setBrandDataSource([]);
            onChange({
              ...value,
              boardCreateType: v,
              boardType: undefined,
              boardId: undefined,
            });
          }}
        >
          <Radio value="algo">算法</Radio>
          <Radio value="manual">人工</Radio>
        </Radio.Group>
      </FormItem>
      <FormItem label="榜单类型">
        <Radio.Group
          disabled={!value?.boardCreateType || isPreview}
          value={value.boardType}
          onChange={(v: BoardType) => {
            setBrandDataSource([]);
            onChange({ ...value, boardType: v, boardId: undefined });
          }}
        >
          {brandTypeDataSource?.map((item) => {
            return (
              <Radio key={item.value} value={item.value}>
                {item.label}
              </Radio>
            );
          })}
        </Radio.Group>
      </FormItem>
      <FormItem label="榜单">
        <CascaderSelect
          changeOnSelect
          dataSource={brandDataSource}
          disabled={!value?.boardCreateType || !value?.boardType}
          isPreview={isPreview}
          showSearch
          style={{ minWidth: 300 }}
          listStyle={{ minWidth: 300 }}
          value={value?.boardId ? value.boardId : ''}
          onChange={(v) => onChange({ ...value, boardId: v as string })}
          onSearch={(v) => {
            // 算法无需动态数据
            if (value.boardCreateType === 'algo') {
              return;
            }
            onSearch.current(v, value.boardCreateType, value.boardType);
          }}
        />
      </FormItem>
      <FormItem label="榜单权重">
        <NumberPicker
          style={{ width: 120 }}
          isPreview={isPreview}
          value={value?.boardWeight}
          min={0}
          max={999999}
          onChange={(v) => onChange({ ...value, boardWeight: v })}
        />
      </FormItem>
    </div>
  );
};
export default BoardItem;
