import './styles.scss';

import { useState } from 'react';

import { Props, Value } from 'BoardSelect/types';
import BoardItem from './components/BoardItem';
import { genId } from './utils';

// 门店类目 详情，Value: string[];
export default function Preview({ env, value }: Props) {
  const [boardList] = useState<Value[]>(
    value?.length > 0 ? value.map((item) => ({ ...item, key: genId() })) : []
  );

  return (
    <div className="nr-board-select">
      {boardList?.map((item, i) => (
        <BoardItem
          key={item.key}
          currentNum={i + 1}
          env={env}
          value={item}
          isPreview={true}
        />
      ))}
    </div>
  );
}
