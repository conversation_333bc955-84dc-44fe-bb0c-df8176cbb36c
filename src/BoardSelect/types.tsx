export interface Props {
  env: 'prod' | 'pre';
  isPreview?: boolean;
  schema: {
    options: {
      limit?: number;
    };
  }
  value?: Value[];

  onChange?: (value: Value[] | undefined) => void;
}

export interface Item {
  value: string;
  label: string;
  children?: Item[];
}

export type BoardType = 'algo' | 'manual';
export interface Value {
  boardCreateType: BoardType; // 'algo' | 'manual'
  boardId: string;
  boardType: string; // 1 人气， 2 品牌， 3 封省
  boardWeight: number; // 权重
  key: number;
}

export interface Response<T> {
  success: boolean;
  data?: T;
  msg?: string;
}