// 价格区间内部状态类型
export interface PriceRange {
  id: number;
  min?: number;
  max?: number;
  desc?: string;
}

// 价格项输出结构
export interface PriceItem {
  startYuan: number;
  endYuan: number;
  text: string;
}

// Schema配置选项
export interface SchemaOptions {
  minRange?: number;
  maxRange?: number;
  minPriceRangeCount?: number;
  maxPriceRangeCount?: number;
  descLimit?: number;
}

// PriceRange组件Props类型
export interface PriceRangeProps {
  env: "prod" | "pre";
  isPreview?: boolean;
  schema?: {
    options: SchemaOptions;
  };
  value?: PriceItem[];
  onChange?: (value: PriceItem[] | undefined) => void;
}

// NumberPicker属性类型
export interface NumberPickerProps {
  min: number;
  max: number;
  placeholder: string;
}
