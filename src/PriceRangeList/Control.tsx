

import React, { useState, useEffect, useMemo, useCallback } from "react";
import { NumberPicker, Button, Message, Input } from "@alifd/next";
import {
  PriceRange,
  PriceItem,
  PriceRangeProps,
  SchemaOptions,
  NumberPickerProps,
} from "./types.d";
import "./styles.scss";
import { calculatePrecise } from "./utils";

// 生成唯一ID的工具函数
export const genId = (() => {
  let _id = 0;
  return () => {
    return ++_id;
  };
})();

const PriceRangeComponent: React.FC<PriceRangeProps> = ({
  schema,
  value,
  onChange,
}) => {
  // 从schema中获取配置选项
  const options = useMemo(
    (): Required<SchemaOptions> => ({
      minRange: schema?.options?.minRange || 0,
      maxRange: schema?.options?.maxRange || 99999,
      minPriceRangeCount: schema?.options?.minPriceRangeCount || 4,
      maxPriceRangeCount: schema?.options?.maxPriceRangeCount || 10,
      descLimit: schema?.options?.descLimit || 15,
    }),
    [schema]
  );

  // 使用state存储数据
  const [priceRanges, setPriceRanges] = useState<PriceRange[]>(() => {
    // 初始化价格区间
    const ranges: PriceRange[] = [];
    for (let i = 0; i < options.minPriceRangeCount; i++) {
      ranges.push({ id: genId(), min: undefined, max: undefined, desc: "" });
    }
    return ranges;
  });

  // 初始化数据
  useEffect(() => {
    if (value && value.length > 0) {
      const ranges = value.map((priceItem) => ({
        id: genId(),
        min: priceItem.startYuan,
        max: priceItem.endYuan,
        desc: priceItem.text || "",
      }));
      // 确保至少有最小数量的区间
      while (ranges.length < options.minPriceRangeCount) {
        ranges.push({ id: genId(), min: undefined, max: undefined, desc: "" });
      }
      setPriceRanges(ranges);
    }
  }, []);

  // 检查是否满足onChange条件并触发
  const checkAndTriggerChange = useCallback((priceRangesData) => {
    if (!onChange) return;

    // 检查是否有足够的完整价格区间
    const completedRanges = priceRangesData.filter(
      (range) =>
        range.min !== undefined &&
        range.max !== undefined &&
        range.min >= 0 &&
        range.max > 0 &&
        calculatePrecise.isGreaterThan(range.max, range.min) &&
        range.desc
    );

    if (completedRanges.length >= options.minPriceRangeCount) {
      // 检查第minPriceRangeCount个区间是否完整
      const targetIndex = options.minPriceRangeCount - 1;
      const targetRange = priceRangesData[targetIndex];
      if (
        targetRange &&
        targetRange.min !== undefined &&
        targetRange.max !== undefined &&
        targetRange.min >= 0 &&
        targetRange.max > 0 &&
        targetRange.desc
      ) {
        const priceItems: PriceItem[] = completedRanges.map((range) => ({
          startYuan: range.min!,
          endYuan: range.max!,
          text: range.desc!,
        }));

        // 只有当值真正发生变化时才触发onChange
        const currentValueStr = JSON.stringify(value);
        const updatedValueStr = JSON.stringify(priceItems);
        if (currentValueStr !== updatedValueStr) {
          onChange(priceItems);
        }
        return;
      } else {
        if (value !== undefined) {
          onChange(undefined);
        }
      }
    } else {
      if (value !== undefined) {
        onChange(undefined);
      }
    }
  }, [priceRanges, options.minPriceRangeCount, value, onChange]);

  // 添加价格区间
  const addPriceRange = (): void => {
    if (priceRanges.length >= options.maxPriceRangeCount) {
      Message.warning(`最多只能配置${options.maxPriceRangeCount}条价格区间`);
      return;
    }
    setPriceRanges([
      ...priceRanges,
      { id: genId(), min: undefined, max: undefined, desc: "" },
    ]);

    // 直接调用检查函数
    checkAndTriggerChange([
      ...priceRanges,
      { id: genId(), min: undefined, max: undefined, desc: "" },
    ]);
  };

  // 删除价格区间
  const removePriceRange = (id: number): void => {
    if (priceRanges.length <= options.minPriceRangeCount) {
      Message.warning(`最少需要配置${options.minPriceRangeCount}条价格区间`);
      return;
    }
    setPriceRanges(priceRanges.filter((range) => range.id !== id));

    // 直接调用检查函数
    checkAndTriggerChange(priceRanges.filter((range) => range.id !== id));
  };

  // 更新价格区间
  const updatePriceRange = (
    id: number,
    field: keyof PriceRange,
    inputValue: number | string | undefined
  ): void => {
    if (field === "id") return; // 不允许修改ID

    const ranges = priceRanges.map((range) => {
      if (range.id === id) {
        if (field === "desc") {
          // 描述字段限制字符数
          const descValue = String(inputValue || "").trim();
          if (descValue.length > options.descLimit) {
            Message.warning(`描述最多输入${options.descLimit}个字符`);
            return range;
          }
          return { ...range, [field]: descValue };
        } else if (field === "min" || field === "max") {
          // 数字字段处理
          const numValue =
            typeof inputValue === "number"
              ? inputValue
              : inputValue
              ? Number(inputValue)
              : undefined;
          return { ...range, [field]: numValue };
        }
      }
      return range;
    });

    setPriceRanges(ranges);

    // 直接调用检查函数
    checkAndTriggerChange(ranges);
  };

  // 获取NumberPicker的动态限制和placeholder
  const getNumberPickerProps = (
    id: number,
    field: keyof PriceRange
  ): NumberPickerProps => {
    const range = priceRanges.find((r) => r.id === id);
    if (!range) {
      return {
        min: options.minRange,
        max: options.maxRange,
        placeholder: `${options.minRange}-${options.maxRange}`,
      };
    }

    let min = options.minRange;
    let max = options.maxRange;
    let placeholder = `${options.minRange}-${options.maxRange}`;

    if (field === "min") {
      // 最小值的最大范围为最大值的最大范围-1，使用BigNumber精确计算
      max = calculatePrecise.subtract(options.maxRange, 1);
      if (range.max !== undefined && range.max > 0) {
        // 如果已输入最大值，则最小值的最大限制是当前最大值-1
        const calculatedMax = calculatePrecise.subtract(range.max, 1);
        max = Math.min(max, calculatedMax);
      }
      placeholder = `${min}-${max}`;
    } else if (field === "max") {
      // 最大值的最小范围为最小值的最小范围+1，使用BigNumber精确计算
      min = calculatePrecise.add(options.minRange, 1);
      if (range.min !== undefined && range.min > 0) {
        // 如果已输入最小值，则最大值的最小限制是当前最小值+1
        const calculatedMin = calculatePrecise.add(range.min, 1);
        min = Math.max(min, calculatedMin);
      }
      placeholder = `${min}-${max}`;
    }

    return {
      min,
      max,
      placeholder,
    };
  };

  return (
    <div className="price-range-component">
      {priceRanges.map((range) => {
        const minProps = getNumberPickerProps(range.id, "min");
        const maxProps = getNumberPickerProps(range.id, "max");

        return (
          <div key={range.id} className="price-range-item">
            <NumberPicker
              value={range.min}
              onChange={(value) => updatePriceRange(range.id, "min", value)}
              // @ts-ignore
              placeholder={minProps.placeholder}
              min={minProps.min}
              max={minProps.max}
              precision={0}
              className="price-input"
            />
            <span className="separator">{`<=X<`}</span>
            <NumberPicker
              value={range.max}
              onChange={(value) => updatePriceRange(range.id, "max", value)}
              // @ts-ignore
              placeholder={maxProps.placeholder}
              min={maxProps.min}
              max={maxProps.max}
              precision={0}
              className="price-input"
            />
            <span className="unit">元</span>
            <Input
              value={range.desc || ""}
              onChange={(value) => updatePriceRange(range.id, "desc", value)}
              placeholder={`C端展示文案：最多${options.descLimit}个字`}
              maxLength={options.descLimit}
              className="desc-input"
            />
            {priceRanges.length > options.minPriceRangeCount && (
              <Button
                text
                type="primary"
                onClick={() => removePriceRange(range.id)}
                className="delete-btn"
              >
                删除
              </Button>
            )}
          </div>
        );
      })}

      {priceRanges.length < options.maxPriceRangeCount && (
        <Button text type="primary" onClick={addPriceRange} className="add-btn">
          新增
        </Button>
      )}
    </div>
  );
};

export default PriceRangeComponent;