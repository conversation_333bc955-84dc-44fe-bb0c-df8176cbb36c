import BigNumber from 'bignumber.js';

// 生成唯一ID的工具函数
export const genId = (() => {
  let _id = 0;
  return () => {
    return ++_id;
  };
})();

// 使用BigNumber进行精确数值计算的工具函数
export const calculatePrecise = {
  // 精确减法
  subtract: (a: number, b: number): number => {
    return new BigNumber(a).minus(new BigNumber(b)).toNumber();
  },
  // 精确加法
  add: (a: number, b: number): number => {
    return new BigNumber(a).plus(new BigNumber(b)).toNumber();
  },
  // 精确比较 - 大于
  isGreaterThan: (a: number, b: number): boolean => {
    return new BigNumber(a).isGreaterThan(new BigNumber(b));
  }
};
