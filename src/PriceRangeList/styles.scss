.price-range-component {
  .section-header {
    margin-bottom: 16px;
    
    .label {
      font-weight: 500;
      color: #333;
    }
  }

  .price-range-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 8px;

    .price-input {
      width: 120px;
      
      .next-number-picker-input {
        text-align: center;
      }
    }

    .separator {
      color: #666;
      font-size: 14px;
      margin: 0 4px;
    }

    .unit {
      color: #666;
      font-size: 14px;
      margin: 0 8px 0 4px;
    }

    .desc-input {
      width: 300px;
      margin-right: 8px;
    }

    .delete-btn {
      color: #ff4d4f;
      margin-left: 8px;
      
      &:hover {
        color: #ff7875;
      }
    }
  }

  .add-btn {
    color: #1890ff;
    margin-top: 8px;
    
    &:hover {
      color: #40a9ff;
    }
  }
}
