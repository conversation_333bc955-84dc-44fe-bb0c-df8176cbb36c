import React from "react";
import { PriceRangeProps } from "./types";

const Component: React.FC<PriceRangeProps> = ({ value }) => {
  return (
    <div className="price-range-component">
      {value?.map((range) => {
        return (
          <div key={range.start + range.text} className="price-range-item">
            价格区间{range.startYuan}元 - {range.endYuan}元, C端展示文案: {range.text}
          </div>
        );
      })}
    </div>
  );
};

export default Component;
