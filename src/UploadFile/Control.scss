.nr-upload-file {
  
  .next-upload-drag-icon{
  }
  .next-upload-drag{
    height: 124px;
  }
  .next-icon::before{
    width: 40px;
    height: 40px;
  }
  .next-icon{
    font-weight: lighter;
  }
  .next-upload-drag-text{
    font-family: PingFangSC-Regular;
    font-size: 16px;
    line-height: 16px;
    color: #333333;
    margin-top: 10px;
    margin-bottom: 0;
  }
  .next-upload-drag-hint{
    font-family: PingFangSC-Regular;
    font-size: 12px;
    line-height: 12px;
    margin-top: 10px;
    color: #999999;
  }
  .next-upload-drag-onDragOver{
    background: rgba(255,112,0,0.04);
    border: 1px dashed #FF7000;
  }
  .next-upload-list-item-name-wrap::before{
    content: "";
    display: inline-block;
    background-image: url("https://img.alicdn.com/imgextra/i3/O1CN0135Cc4e27fRXXEvkBp_!!6000000007824-2-tps-12-12.png");
    height: 12px;
    width: 12px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
  }
  .next-upload-list-item{
    position: relative;
    text-align: left;
    font-size: 12px;
    font-family: PingFangSC-Regular;
    padding-left: 30px;
  }
  .next-upload-list-item-done{
    background-color: rgb(250,250,250);
    color: rgb(147,147,147);;
    color: #666666;
  }
  .next-upload-list-item-error{
    background: rgba(255,68,51,0.06);
  }
  .next-upload-list-item-error::after{
    content: "文件格式错误";
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #FF4433;
    text-align: right;
    display: inline-block;
    position: absolute;
    right: 36px;
    top: 50%;
    transform: translateY(-50%);
  }
}
