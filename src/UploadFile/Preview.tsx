import React from "react";
import { Props } from "./types";

// 上传文件 详情，Value: string;
export default function Preview({ value }: Props) {
  const curValue =
    value && typeof value === "string" ? JSON.parse(value) : value;

  if (!curValue) {
    return null;
  }

  return (
    <a
      target="_blank"
      download={curValue?.fileName}
      href={curValue?.ossFileUrl}
    >
      {curValue?.fileName}
    </a>
  );
}
