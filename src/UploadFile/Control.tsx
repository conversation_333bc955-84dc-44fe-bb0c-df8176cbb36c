import "./Control.scss";

import { useState } from "react";
import { Icon, Message, Upload } from "@alifd/next";

import { Props } from "./types";
import { apiHostMapping } from "pkg/request";

// 上传文件
export default function Control({ env, schema, value, onChange }: Props) {
  const schemaOptions = schema.options || ({} as { templateUrl: string });
  const curValue =
    value && typeof value === "string" ? JSON.parse(value) : value;
  // 改变上传拖拽颜色
  const [isDrag, setIsDrag] = useState(false);
  // 用来展示
  const [filesData, setFilesData] = useState(
    curValue
      ? [
          {
            uid: curValue?.ossFileUrl,
            name: curValue?.fileName || curValue?.ossFileUrl,
            state: "done",
            url: curValue?.ossFileUrl,
            downloadURL: curValue?.ossFileUrl,
          },
        ]
      : undefined
  );

  const beforeUpload = (file: File, options: any) => {
    // 在上传之前判断类型，如果类型不匹配直接失败。
    return new Promise((resolve, reject) => {
      if (
        file.type ==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type == "application/vnd.ms-excel"
      ) {
        resolve(options);
      } else {
        setIsDrag(false);
        reject();
        Message.error("文件类型不匹配");
      }
    });
  };

  const onDragOver = () => {
    // 设置上传背景颜色
    setIsDrag(true);
  };
  const onDragLeave = () => {
    // 取消上传背景颜色
    setIsDrag(false);
  };

  const onRemove = (removeItem: { url: string }) => {
    // 每次删除都要将DefaultValue的值重新刷一遍，避免重复出现
    setFilesData(undefined);
    onChange(null);
  };

  const onSuccess = (info: {
    response: {
      code: string;
      data: { fileName: string; ossFileUrl: string; uid: string };
      msg: string;
    };
  }) => {
    setIsDrag(false);
    const res = info.response;
    if (Number(res.code) === 200) {
      setFilesData([
        {
          uid: res.data.uid || res.data.ossFileUrl,
          name: res.data.fileName,
          state: "done",
          url: res.data.ossFileUrl,
          downloadURL: res.data.ossFileUrl,
        },
      ]);
      onChange({
        fileName: res.data.fileName,
        ossFileUrl: res.data.ossFileUrl,
      });
    } else {
      Message.error(res.msg || "上传失败");
    }
  };

  return (
    <div className="nr-upload-file">
      <div style={{ margin: "7px 0 10px 0" }}>
        请先下载
        <a
          target="_blank"
          download="说明模板.csv"
          href={schemaOptions.templateUrl}
        >
          导入模板
        </a>
        ，按照要求填写完成，上传到系统。
      </div>
      <Upload.Dragger
        headers={{ "X-Requested-With": null }}
        action={`${apiHostMapping[env]}/open/upload/commonFileReturnOssFileUr`}
        onSuccess={onSuccess}
        listType="text"
        onDragOver={onDragOver}
        onDragLeave={onDragLeave}
        beforeUpload={beforeUpload}
        value={filesData}
        onRemove={onRemove}
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
      >
        <div
          className={`next-upload-drag ${
            isDrag ? "next-upload-drag-onDragOver" : ""
          }`}
        >
          <p className="next-upload-drag-icon">
            <Icon type="upload" />
          </p>
          <p className="next-upload-drag-text">点击或将文件拖拽到这里上传</p>
          <p className="next-upload-drag-hint">支持扩展名：.xlsx,.xls</p>
        </div>
      </Upload.Dragger>{" "}
    </div>
  );
}
